<script setup lang="ts">
import { ref, watch, onUnmounted, onMounted } from 'vue';
import { onLoad, onShow, onUnload, onHide } from '@dcloudio/uni-app';
import ai from '@/common/ai';
import dayjs from '@/utils/day';
import { getClientAppDetail, updateApp } from '@/api/app-center';
import { getBaseUrl } from '@/common/ai/url';
// AI动画src
const aiAnimationSrc = ref(
  'https://huayun-ai-obs-public.huayuntiantu.com/eabaa1b140d10f8f54b6b96aba28d8dd.gif'
);
const defaultAvatarSrc =
  'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg';
// 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/7a76281c921b3e0cbbcb7749e300d749.svg';
const isLoadingAiAvatar = ref(false);
const utoastRef = ref();

const form = ref({
  id: '',
  name: '',
  intro: '',
  avatarUrl: defaultAvatarSrc,
});
const signal = ai.AbortSignal();

onLoad(options => {
  const id = options?.id;
  if (id) {
    getClientAppDetail(id).then(res => {
      console.log('onLoad--edit-app', res);
      form.value = res;
    });
  }
});

onUnmounted(() => {
  signal.abort();
});

async function generateAvatar() {
  if (form.value.name.trim().length < 5) {
    utoastRef.value?.show({
      message: '应用名称不能少于5个字符',
      type: 'info',
    });
    return;
  }

  isLoadingAiAvatar.value = true;
  try {
    const reuslt = await ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: JSON.stringify({
              name: form.value.name,
              intro: form.value.intro,
            }),
          },
        ],
        type: 9, // 头像生成
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
        stream: true,
      },
      onMessage: message => {
        console.log(message);
        if (message.event === 'fastAnswer') {
          let data: any = {};
          try {
            data = message.text ? JSON.parse(message.text) : {};
          } catch (err) {
            console.warn('JSON parse error:', err);
            data = {};
          }
          form.value.avatarUrl = data?.avatar ?? defaultAvatarSrc;
          console.log({ data });
        }
      },
      abortSignal: signal,
    });
    return reuslt;
  } catch (error) {
    console.log(error);
  } finally {
    isLoadingAiAvatar.value = false;
  }
}

function fetchUpdateApp() {
  return updateApp({
    id: form.value.id!,
    name: form.value.name,
    intro: form.value.intro,
    avatarUrl: form.value.avatarUrl,
  });
}

// 保存应用
function handleSaveApp() {
  fetchUpdateApp()
    .then(res => {
      if (res) {
        utoastRef.value?.show({
          message: '保存成功',
          type: 'success',
        });
      }
    })
    .catch(err => {
      utoastRef.value?.show({
        message: '保存失败',
        type: 'info',
      });
    });
}

function handleChooseAvatar() {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: res => {
      const tempFilePaths = res.tempFilePaths;
      // @ts-ignore - 处理tempFilePaths类型问题
      tempFilePaths.forEach((filePath: string) => {
        uploadFile(filePath);
      });
    },
  });
}

// 上传文件
const uploadFile = (filePath: string) => {
  const baseUrl = getBaseUrl();
  uni.uploadFile({
    url: `${baseUrl}/huayun-ai/system/file/public/upload`,
    header: {
      Authorization: uni.getStorageSync('token'),
    },
    filePath: filePath,
    name: 'file',
    success: uploadFileRes => {
      // @ts-ignore - parse返回值类型
      const result = JSON.parse(uploadFileRes.data);
      if (result.data.fileUrl) {
        form.value.avatarUrl = result.data.fileUrl;
      }
    },
  });
};
</script>

<template>
  <LkToast ref="utoastRef"></LkToast>
  <view class="container">
    <u-navbar title="编辑应用" :autoBack="true" placeholder></u-navbar>
    <view class="content">
      <!-- 头像 -->
      <view class="avatar-container">
        <view class="avatar">
          <image v-show="isLoadingAiAvatar" :src="aiAnimationSrc" />
          <image
            v-show="!isLoadingAiAvatar"
            :src="form.avatarUrl || defaultAvatarSrc"
            @click="handleChooseAvatar"
          />
        </view>
        <view class="ai-created">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/8a1e6c046e614ea32e8847cb6fa9eecf.svg"
          ></image>
          <view class="ai-created-text" @click="generateAvatar">{{
            isLoadingAiAvatar ? 'AI正在生成中' : 'AI创建'
          }}</view>
        </view>
      </view>

      <!-- 应用名称 -->
      <view class="app-name">
        <view class="app-name-text">应用描述 <text class="required">*</text></view>
        <up-input
          v-model="form.name"
          class="app-name-input"
          placeholder="请输入内容"
          border="surround"
        ></up-input>
        <!-- 应用介绍 -->
        <view class="app-description">
          <view class="app-description-text">应用介绍 </view>
          <up-textarea
            v-model="form.intro"
            class="app-description-textarea"
            placeholder="请输入内容"
            count
            maxlength="500"
          ></up-textarea>
        </view>
      </view>
    </view>

    <LkButton
      class="confirm-create-btn"
      type="primary"
      size="large"
      shape="round"
      :disabled="form?.name?.trim().length < 5"
      @click="handleSaveApp"
      >保存</LkButton
    >
  </view>
</template>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;

  .confirm-create-btn {
    margin-top: auto;
    margin-bottom: 30 * 2rpx;
  }
}

.content {
  .avatar-container {
    position: relative;
  }
  .avatar {
    position: relative;
    width: 112 * 2rpx;
    height: 112 * 2rpx;
    border-radius: 50%;
    background-color: #fff;
    border: 5 * 2rpx solid #fff;
    margin: 0 auto;
    margin-top: 24 * 2rpx;
    margin-bottom: 20 * 2rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .ai-created {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 12 * 2rpx;
    border-radius: 50 * 2rpx;
    padding: 2 * 2rpx 12 * 2rpx;
    background: linear-gradient(90deg, #58aeff 0%, #9f71ff 46.5%, #ff9ed4 100%);

    image {
      width: 14 * 2rpx;
      height: 14 * 2rpx;
    }
  }

  .required {
    color: #ff4d4f;
  }
  .app-description {
    margin-top: 16 * 2rpx;
  }

  .app-name-text,
  .app-description-text {
    color: #1d2129;
    font-size: 16 * 2rpx;
    font-weight: 500;
    margin-bottom: 10 * 2rpx;
  }

  .app-name-input {
    height: 56 * 2rpx;
    padding: 16 * 2rpx;
    background-color: #fff;
    border-radius: 12 * 2rpx;
    border: none;
  }

  .app-description-textarea {
    height: 285 * 2rpx;
    padding: 16 * 2rpx;
    padding-bottom: 30rpx;
    background-color: #fff;
    border-radius: 12 * 2rpx;
    border: none;
  }
  ::v-deep .u-textarea__field {
    height: 100% !important;
  }
}
</style>
