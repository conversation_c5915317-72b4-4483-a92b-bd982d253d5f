<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, onUnmounted } from 'vue';
import dayjs from '@/utils/day';
import { useUserStore } from '@/store/userStore';
import ai from '@/common/ai';
import { defaultAppTemplates, simpleTemplate } from '@/fastgpt/web/core/app/templates';
import { createApp } from '@/api/app-center';
import { AppTypeEnum, ModeTypeEnum } from '@/constants/api/app';
import { onHide } from '@dcloudio/uni-app';
// 应用描述字符
const appDescribe = ref('');
const uToastRef = ref(null);
const aiProgressText = ref('AI正在生成应用名称、介绍');
const createAppLoading = ref(false);
const signal = ai.AbortSignal();

onUnmounted(() => {
  signal.abort();
});

function showToast(params: any) {
  if (uToastRef.value) {
    (uToastRef.value as any).show({
      ...params,
      complete() {
        if (params.url) {
          uni.navigateTo({
            url: params.url,
          });
        }
      },
    });
  }
}

async function hangleAICreatedApplition() {
  createAppLoading.value = true;
  try {
    const result = await ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: JSON.stringify({
              creation_requirement: appDescribe.value,
            }),
          },
        ],
        stream: true,
        detail: true,
        type: 8,
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      },
      onMessage: message => {
        console.log('onMessage', message);
        if (message.event === 'flowNodeStatus') {
          aiProgressText.value = message.name || '';
        }
      },
      abortSignal: signal,
    });
    return result;
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
  } finally {
    createAppLoading.value = false;
    aiProgressText.value = 'AI正在生成应用名称、介绍';
  }
}

async function handleCreateApp() {
  if (appDescribe.value.trim().length === 4) {
    showToast({
      type: 'info',
      message: '创建应用描述不得少于4个字',
    });
    return;
  }

  try {
    const result = await hangleAICreatedApplition();
    // 如果result为undefined，则不进行创建
    if (result === undefined) return;
    // @ts-ignore
    const aiParmas = result ? JSON?.parse(result?.responseText) : {};
    console.log('aiParmas', aiParmas);

    // TODO: 创建应用
    // 创建应用参数
    const createAppParams = {
      avatarUrl: aiParmas?.avatar || '',
      name: aiParmas?.name || '',
      intro: aiParmas?.intro || '',
      type: AppTypeEnum.simple,
      mode: ModeTypeEnum.simple,
      isAICreated: 1,
      edges: simpleTemplate.edges || [],
      modules: simpleTemplate.modules || [],
      chatConfig: {
        fileSelectConfig: {
          maxFiles: 20,
          canSelectFile: true,
          canSelectImg: true,
          canAutoParse: false,
          canParseORC: false,
        },
      },
    };
    createApp(createAppParams).then((res: any) => {
      console.log('创建成功APP', res);
      // 创建成功跳转页面,传递数据过去
      uni.navigateTo({
        url: `/pages-subpackages/app-center-pkg/create-app-success/index?id=${res.id}`,
        success: () => {
          console.log('createAppSuccess跳转成功', res);
        },
      });
    });
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
  } finally {
  }
}

function cancelCreateApp() {
  createAppLoading.value = false;
  signal.abort();
}

// TODO: 测试代码跳转成功页面
function handleGotoSuccess() {
  uni.navigateTo({
    url: '/pages-subpackages/app-center-pkg/create-app-success/index',
  });
}
</script>

<template>
  <LkToast ref="uToastRef"></LkToast>

  <up-modal class="create-app-modal" :show="createAppLoading" :showConfirmButton="false">
    <view ref="animationContainer" class="modal-content">
      <!-- <image
        class="lottie-image"
        src="https://huayun-ai-obs-public.huayuntiantu.com/a05492879292970f7374926e54d72eb0.gif"
        mode="scaleToFill"
      /> -->
      <c-lottie
        class="lottie-image"
        ref="cLottieRef"
        src="https://huayun-ai-obs-public.huayuntiantu.com/cb807f326d83d36d7f894d67017ec6d9.json"
        :loop="true"
        renderer="svg"
        width="264rpx"
        height="230rpx"
      ></c-lottie>
      <view class="lottie-text">{{ aiProgressText }}</view>
      <LkButton
        class="lottie-button"
        type="primary"
        size="large"
        shape="round"
        @click="cancelCreateApp"
        >取消创建</LkButton
      >
    </view>
  </up-modal>

  <view class="container">
    <u-navbar :autoBack="true" bgColor="#f6f6fc" border placeholder>
      <template #center>
        <text class="navbar-title">AI创建应用</text>
      </template>
    </u-navbar>
    <!-- 代码写在下面 -->
    <view class="tips">
      <view class="description">
        💡
        <text class="description-text" selectable>示例：</text
        >你是语文作文批改助手，专注于语文作文批改，能从语法、结构、文采等多方面给出详细评语和改进建议</view
      >
    </view>

    <view class="app-describe">
      <view class="app-describe-title" @tap="handleGotoSuccess"
        >应用描述 <text class="required-text">*</text></view
      >
      <view class="app-describe-content">
        <up-textarea
          class="app-describe-textarea"
          v-model="appDescribe"
          placeholder="请输入内容"
          count
          maxlength="500"
          autoHeight
        ></up-textarea>
      </view>
    </view>

    <LkButton
      @click="handleCreateApp"
      class="create-app-button"
      type="primary"
      size="large"
      shape="round"
    >
      确认创建
    </LkButton>
  </view>
</template>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 100vh;
  background-color: #f6f6fc;
  padding: 0 16 * 2rpx;
  overflow: hidden;
}

.navbar-title {
  background-image: -webkit-linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  background-image: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

.tips {
  /* width: 343px; */
  height: 89 * 2rpx;
  border-radius: 14 * 2rpx;
  background-color: #fff;
  margin-top: 15 * 2rpx;
  padding: 12 * 2rpx;
  color: #1d2129;
  font-size: 14 * 2rpx;
  line-height: 22 * 2rpx;

  .description-text {
    background-image: -webkit-linear-gradient(114deg, #3472ff -18.56%, #9452ff 108.66%);
    background-image: linear-gradient(114deg, #3472ff -18.56%, #9452ff 108.66%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }

  ::v-deep .u-textarea__field {
    height: 0 !important;
  }
}

.app-describe {
  .app-describe-title {
    display: flex;
    align-items: center;
    color: #1d2129;
    font-size: 16 * 2rpx;
    font-weight: 500;
    line-height: 24 * 2rpx;
    margin-top: 16 * 2rpx;
    margin-bottom: 10 * 2rpx;
    .required-text {
      color: #f53f3f;
      margin-left: 4 * 2rpx;
    }
  }
  .app-describe-content {
    height: 360 * 2rpx;
    .app-describe-textarea {
      height: 100%;
      border-radius: 14 * 2rpx;
      border: none;
      color: #1d2129;
    }
    ::v-deep .u-textarea__field {
      height: 100% !important;
    }
  }
}
.create-app-button {
  width: 100%;
  margin-top: auto;
  margin-bottom: 30 * 2rpx;
}

.create-app-modal {
  ::v-deep .u-modal__content {
    padding: 0 !important;
  }
  .modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* justify-content: center; */
    width: 100%;
    height: 339 * 2rpx;
    padding: 24 * 2rpx !important;
  }
  .lottie-image {
    width: 132 * 2rpx;
    height: 115 * 2rpx;
    margin-top: 45 * 2rpx;
  }
  .lottie-text {
    font-size: 14 * 2rpx;
    line-height: 22 * 2rpx;
    color: #4e5969;
  }
  .lottie-button {
    width: 100%;
    background-color: #f3ecff;
    color: #7d4dff;
    font-size: 16 * 2rpx;
    margin-top: auto;
  }
}
</style>
