<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useUserStore } from '@/store/userStore';
import {
  getSmsCode,
  getAuthValid,
  loginByPassword,
  login,
  getTenantListByJsCode,
  resetPassword,
  bindPhoneToWechat,
} from '@/api/auth';
import CryptoJS from 'crypto-js';
import { serviceImageData } from '@/components/LkServiceImage/data';
import { LoginType } from '@/constants/auth';
import PasswordSetting from './PasswordSetting.vue';
import PhoneBinding from './PhoneBinding.vue';
import { useTabStore } from '@/store/tabStore';
import { TabEnum } from '@/constants';
const userStore = useUserStore();

// 导航方法
function back(nav = true) {
  uni.navigateBack();
}

function toForgotPassword() {
  uni.navigateTo({
    url: '/pages-subpackages/auth-pkg/forgot-password/index',
  });
}

function toTerms() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/about/terms',
  });
}

function toPrivacy() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/about/privacy',
  });
}

// 登录类型
const loginType = ref(LoginType.PASSWORD);

// 登录表单数据
const loginForm = reactive({
  phone: '',
  code: '',
  username: '',
  password: '',
});

// 登录状态
const isLoading = ref(false);
const codeTip = ref('');
const uCode = ref<any>(null);
const slideCodeRef = ref();
const protocolAgreed = ref(false);
const uToastRef = ref();

// 密码设置弹窗引用
const passwordSettingRef = ref();

// 手机号绑定弹窗引用
const phoneBindingRef = ref();

// 保存微信登录的code，用于后续绑定
const wechatLoginCode = ref('');

// 新增样式配置
const inputStyle = {
  height: '92rpx',
  background: '#FFFFFF',
  borderRadius: '100rpx',
  padding: '0 48rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#AEAEB2',
  fontSize: '28rpx',
  fontWeight: '400',
};

const buttonStyle = {
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
  background: '#7D4DFF',
  marginBottom: '40rpx',
};

// 获取背景图
function getBgImage() {
  return {
    background: `url(${serviceImageData.bgImage}) 0px 0px /100% auto no-repeat #f7f7f7`,
  };
}

// 切换登录方式
function switchLoginType(type: LoginType) {
  loginType.value = type;
  loginForm.code = '';
}

// 获取短信验证码
async function getSmsCodeFn() {
  if (!uCode.value.canGetCode) return;

  if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  try {
    const slideParams = await slideCodeRef.value.open();
    await getSmsCode({
      mobile: loginForm.phone,
      ...slideParams,
    });

    uToastRef.value.show({
      message: '验证码已发送',
      type: 'success',
    });
    uCode.value.start();
  } catch (error: any) {
    uCode.value.reset();
  }
}

// 处理登录
async function handleLogin() {
  if (!protocolAgreed.value) {
    uToastRef.value.show({
      message: '请勾选同意后再登录',
      type: 'info',
    });
    return;
  }

  if (loginType.value === LoginType.PHONE) {
    if (!loginForm.phone || !/^1\d{10}$/.test(loginForm.phone)) {
      uToastRef.value.show({
        message: '请输入正确的手机号',
        type: 'info',
      });
      return;
    }
    if (!loginForm.code) {
      uToastRef.value.show({
        message: '请输入验证码',
        type: 'info',
      });
      return;
    }
  } else {
    if (!loginForm.username) {
      uToastRef.value.show({
        message: '请输入手机号',
        type: 'info',
      });
      return;
    }
    if (!loginForm.password) {
      uToastRef.value.show({
        message: '请输入密码',
        type: 'info',
      });
      return;
    }
  }

  isLoading.value = true;
  try {
    const slideParams = await slideCodeRef.value.open();
    let tenantList;

    if (loginType.value === LoginType.PHONE) {
      tenantList = await getAuthValid({
        mobile: loginForm.phone,
        code: loginForm.code,
        ...slideParams,
      });
    } else {
      tenantList = await loginByPassword({
        account: loginForm.username,
        password: CryptoJS.SHA256(loginForm.password).toString(CryptoJS.enc.Hex),
      });
    }

    const userInfo = await login({
      accessKey: tenantList[0].accessKey,
    });
    userStore.loginSuccess(userInfo, tenantList);

    // 检查是否首次登录
    if (userInfo.firstLogin) {
      passwordSettingRef.value?.open();
      return;
    }

    uToastRef.value.show({
      message: '登录成功',
      type: 'success',
    });

    setTimeout(() => {
      const tabStore = useTabStore();
      tabStore.switchTab(TabEnum.HOME);
      uni.navigateTo({
        url: '/pages/index/index',
      });
    }, 1000);
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '登录失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
}

// 处理密码修改成功
const handlePasswordSuccess = async (encryptedPassword: string) => {
  try {
    // 这里需要调用修改密码的API
    await resetPassword({
      password: encryptedPassword,
      password1: encryptedPassword,
    });

    uToastRef.value.show({
      message: '密码修改成功',
      type: 'success',
    });

    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/home/<USER>',
      });
    }, 1500);
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '密码修改失败',
      type: 'info',
    });
  }
};

// 处理微信登录
async function handleWechatLogin() {
  // #ifdef APP-PLUS
  const authProvider = 'weixin' as 'weixin';
  uni.getProvider({
    service: 'oauth',
    success: async function (res: any) {
      if (~res.provider.indexOf(authProvider)) {
        uni.login({
          provider: authProvider,
          success: async function (loginRes: any) {
            try {
              // 保存微信登录的code，用于后续绑定
              wechatLoginCode.value = loginRes.code;

              // 获取租户列表
              const tenantList = await getTenantListByJsCode({
                jsCode: loginRes.code,
              });

              if (!tenantList?.length) {
                // 没有找到租户，显示手机号绑定弹窗
                phoneBindingRef.value?.open();
                return;
              }

              // 使用第一个租户进行登录
              const userInfo = await login({
                accessKey: tenantList[0].accessKey,
              });

              userStore.loginSuccess(userInfo, tenantList);

              uToastRef.value.show({
                message: '登录成功',
                type: 'success',
              });

              setTimeout(() => {
                const tabStore = useTabStore();
                tabStore.switchTab(TabEnum.HOME);
                uni.navigateTo({
                  url: '/pages/index/index',
                });
              }, 1000);
            } catch (error: any) {
              uToastRef.value.show({
                message: error.message || '登录失败',
                type: 'info',
              });
            }
          },
          fail: function (err) {
            uToastRef.value.show({
              message: '微信登录失败',
              type: 'info',
            });
            console.error('微信登录失败：', err);
          },
        });
      } else {
        uToastRef.value.show({
          message: '当前环境不支持微信登录',
          type: 'info',
        });
      }
    },
  });
  // #endif
}

// 处理手机号绑定成功
const handlePhoneBindingSuccess = async (bindingData: { phone: string; code: string }) => {
  try {
    // 调用绑定手机号的API，将微信账号与手机号关联
    const tenantList = await bindPhoneToWechat({
      phone: bindingData.phone,
      code: bindingData.code,
      jsCode: wechatLoginCode.value,
    });

    if (tenantList?.length) {
      // 绑定成功且有租户，直接登录
      const userInfo = await login({
        accessKey: tenantList[0].accessKey,
      });

      userStore.loginSuccess(userInfo, tenantList);

      uToastRef.value.show({
        message: '绑定成功，登录成功',
        type: 'success',
      });

      setTimeout(() => {
        const tabStore = useTabStore();
        tabStore.switchTab(TabEnum.HOME);
        uni.navigateTo({
          url: '/pages/index/index',
        });
      }, 1000);
    } else {
      uToastRef.value.show({
        message: '手机号绑定成功',
        type: 'success',
      });
    }
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '手机号绑定失败',
      type: 'info',
    });
  }
};
</script>

<template>
  <view class="login">
    <!-- 状态栏 -->
    <u-navbar @leftClick="back(false)" bgColor="transparent">
      <template #left>
        <view class="back-icon">
          <u-icon name="arrow-left" size="40rpx" color="#585858"></u-icon>
        </view>
      </template>
    </u-navbar>
    <!-- 左上角logo -->
    <view class="login-left-top-logo">
      <LkServiceImage
        name="loginLogo"
        class="login-left-top-logo-image"
        mode="aspectFit"
      ></LkServiceImage>
    </view>
    <!-- 背景图片 -->
    <view class="background">
      <LkServiceImage name="loginBg" class="bg-image" mode="aspectFill"></LkServiceImage>
    </view>

    <!-- Logo区域 -->
    <view class="header-wrap">
      <view class="content">
        <LkServiceImage name="loginIpImage" class="logo" mode="aspectFit"></LkServiceImage>
      </view>
      <text class="slogan">智驱世界，共创美好未来</text>
    </view>

    <!-- 登录区域 -->
    <view class="login-area">
      <!-- 手机号登录 -->
      <template v-if="loginType === LoginType.PHONE">
        <view class="login-form">
          <view class="form-item">
            <u-input
              v-model="loginForm.phone"
              type="number"
              maxlength="11"
              placeholder="请输入手机号"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            ></u-input>
          </view>

          <view class="form-item">
            <u-input
              v-model="loginForm.code"
              type="number"
              maxlength="6"
              placeholder="请输入验证码"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            >
              <template #suffix>
                <up-code
                  ref="uCode"
                  @change="codeTip = $event"
                  seconds="60"
                  changeText="Xs后重发"
                ></up-code>
                <view class="get-code-btn" @click="getSmsCodeFn">{{
                  codeTip || '获取验证码'
                }}</view>
              </template>
            </u-input>
          </view>
        </view>
      </template>

      <!-- 密码登录 -->
      <template v-else>
        <view class="login-form">
          <view class="form-item">
            <u-input
              v-model="loginForm.username"
              type="text"
              placeholder="请输入手机号"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            ></u-input>
          </view>

          <view class="form-item">
            <u-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :customStyle="inputStyle"
              :placeholderStyle="placeholderStyle"
              border="none"
            >
            </u-input>
          </view>
          <view class="forgot-pwd" @click="toForgotPassword">忘记密码</view>
        </view>
      </template>

      <!-- 登录按钮 -->
      <view class="btn-area">
        <u-button
          type="primary"
          @click="handleLogin"
          :loading="isLoading"
          :customStyle="buttonStyle"
          >登录</u-button
        >
      </view>

      <!-- 协议 -->
      <view class="protocol">
        <view class="checkbox" @click="protocolAgreed = !protocolAgreed">
          <view class="checkbox-inner" :class="{ checked: protocolAgreed }">
            <u-icon v-if="protocolAgreed" name="checkmark" color="#FFFFFF" size="24rpx"></u-icon>
          </view>
        </view>
        <text class="protocol-text">我已阅读并同意</text>
        <text class="protocol-link" @click="toTerms">《用户协议、隐私政策》</text>
        <!-- <text class="protocol-text">和</text> -->
        <!-- <text class="protocol-link" @click="toPrivacy">《隐私政策》</text> -->
      </view>

      <!-- 其他登录方式 -->
      <view class="other-login">
        <text class="other-title">其他登录方式</text>
        <view class="other-icons">
          <view
            class="icon-item"
            v-if="loginType === LoginType.PASSWORD"
            @click="switchLoginType(LoginType.PHONE)"
          >
            <LkServiceImage name="phoneSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
          <view
            class="icon-item"
            v-if="loginType === LoginType.PHONE"
            @click="switchLoginType(LoginType.PASSWORD)"
          >
            <LkServiceImage name="passwordSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
          <view class="icon-item" @click="handleWechatLogin">
            <LkServiceImage name="wechatSelectIcon" mode="aspectFit"></LkServiceImage>
          </view>
        </view>
      </view>
    </view>

    <!-- 密码设置弹窗 -->
    <PasswordSetting ref="passwordSettingRef" @success="handlePasswordSuccess" />

    <!-- 手机号绑定弹窗 -->
    <PhoneBinding ref="phoneBindingRef" @success="handlePhoneBindingSuccess" />

    <!-- 滑块验证码组件 -->
    <LkSlideCode ref="slideCodeRef"></LkSlideCode>
    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>

<style lang="scss">
.login {
  min-height: 100vh;
  background: #f1f3f6;
  position: relative;
  .login-left-top-logo {
    position: absolute;
    top: 100rpx;
    left: 20rpx;
    width: 158rpx;
    height: 64rpx;

    .login-left-top-logo-image {
      width: 158rpx;
      height: 64rpx;
      z-index: 10;
    }
  }
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .bg-image {
      width: 100%;
      height: 924rpx;
      position: absolute;
      top: 0;
    }

    .bg-overlay {
      width: 100%;
      height: 584rpx;
      position: absolute;
      bottom: 0;
    }
  }

  .header-wrap {
    padding: 250rpx 104rpx 0rpx;
    position: relative;
    z-index: 1;
    text-align: center;

    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 40rpx;
    }

    .logo {
      width: 550rpx;
      height: 200rpx;
    }

    .slogan {
      font-size: 40rpx;
      color: #280868;
      text-align: center;
      margin-top: 40rpx;
      font-weight: bold;
      letter-spacing: 1.6rpx;
    }
  }

  .login-area {
    position: relative;
    z-index: 1;
    padding: 0 60rpx;
    margin-top: 60rpx;

    .login-form {
      .form-item:not(:last-child) {
        margin-bottom: 32rpx;
      }
      .form-item:nth-child(2) {
        margin-bottom: 20rpx !important;
      }
      .get-code-btn {
        font-size: 30rpx;
        color: #7d4dff !important;
      }
      .forgot-pwd {
        font-size: 28rpx;
        color: #7d4dff;
        text-align: right;
      }
    }

    .btn-area {
      margin-top: 60rpx;
      margin-bottom: 30rpx;
    }

    .protocol {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40rpx 0;

      .checkbox {
        width: 36rpx;
        height: 36rpx;
        margin-right: 16rpx;

        &-inner {
          width: 100%;
          height: 100%;
          border: 2rpx solid #aaa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            background: #7d4dff;
            border-color: #7d4dff;
          }
        }
      }

      .protocol-text {
        font-size: 26rpx;
        color: #000000;
      }

      .protocol-link {
        font-size: 26rpx;
        color: #3d7fff;
      }
    }

    .other-login {
      margin-top: 80rpx;
      text-align: center;

      .other-title {
        display: inline-block;
        font-size: 24rpx;
        color: #414141;
        margin-bottom: 28rpx;
      }

      .other-icons {
        display: flex;
        justify-content: center;
        gap: 52rpx;

        .icon-item {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 80rpx;
            height: 80rpx;
          }
        }
      }
    }
  }
}

.back-icon {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #606060;
}
</style>
