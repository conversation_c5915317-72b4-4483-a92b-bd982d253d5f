<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import CryptoJS from 'crypto-js';

const visible = ref(false);
const isLoading = ref(false);
const uToastRef = ref();

const form = reactive({
  password: '',
  confirmPassword: '',
});

// 样式配置
const inputStyle = {
  height: '96rpx',
  background: '#F2F3F5',
  borderRadius: '16rpx',
  padding: '0 32rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#86909C',
  fontSize: '32rpx',
  fontWeight: '400',
};

const buttonStyle = {
  width: '320rpx',
  height: '84rpx',
};

// 表单验证
const isValid = computed(() => {
  if (!form.password || !form.confirmPassword) return false;

  const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/;
  return passwordPattern.test(form.password) && form.password === form.confirmPassword;
});

// 暴露方法
const emit = defineEmits(['success', 'close']);

// 打开弹窗
const open = () => {
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  emit('close');
};

// 重置表单
const reset = () => {
  form.password = '';
  form.confirmPassword = '';
};

// 处理重置按钮点击
const handleReset = () => {
  reset();
};

// 处理关闭
const handleClose = () => {
  close();
  reset();
};

// 处理确认
const handleConfirm = async () => {
  if (!isValid.value) {
    uToastRef.value.show({
      message: '请检查密码格式是否正确',
      type: 'info',
    });
    return;
  }

  isLoading.value = true;
  try {
    // 发送加密后的密码
    const encryptedPassword = CryptoJS.SHA256(form.password).toString(CryptoJS.enc.Hex);
    emit('success', encryptedPassword);
    close();
    reset();
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '修改失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  open,
  close,
  reset,
});
</script>

<template>
  <u-popup
    :show="visible"
    @close="handleClose"
    mode="bottom"
    round="12"
    :closeable="false"
    :safe-area-inset-bottom="true"
  >
    <view class="password-setting">
      <!-- 标题 -->
      <view class="header">
        <text class="title">密码设置</text>
      </view>

      <!-- 提示文字 -->
      <view class="tips">
        为保障您的账号安全，首次登录请修改您的密码（密码 8 至 16
        位，包含大小写字母和数字的组合，可以输入特殊符号）
      </view>

      <!-- 表单 -->
      <view class="form">
        <view class="form-item">
          <u-input
            v-model="form.password"
            type="password"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            placeholder="请输入新密码"
            border="none"
          ></u-input>
        </view>

        <view class="form-item">
          <u-input
            v-model="form.confirmPassword"
            type="password"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            placeholder="请再次输入新密码"
            border="none"
          ></u-input>
        </view>
      </view>

      <!-- 按钮组 -->
      <view class="button-group">
        <LkButton
          type="neutral"
          size="large"
          class="reset-button button"
          shape="round"
          @click="handleReset"
          :customStyle="buttonStyle"
          >重置</LkButton
        >

        <LkButton
          type="primary"
          size="large"
          class="confirm-button button"
          shape="round"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!isValid"
          :customStyle="buttonStyle"
          >确认修改</LkButton
        >
      </view>
    </view>
  </u-popup>

  <!-- Toast 提示组件 -->
  <LkToast ref="uToastRef"></LkToast>
</template>

<style lang="scss">
.password-setting {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;

  .header {
    text-align: center;
    margin-bottom: 24rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1d2129;
    }
  }

  .tips {
    font-size: 28rpx;
    color: #4e5969;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }

  .form {
    .form-item {
      margin-bottom: 32rpx;
    }
  }

  .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 48rpx;
    padding: 0 32rpx;
    .button {
      width: 48% !important;
    }
  }
}
</style>
