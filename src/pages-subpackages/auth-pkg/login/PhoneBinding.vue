<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { getSmsCode } from '@/api/auth';

const visible = ref(false);
const isLoading = ref(false);
const uToastRef = ref();
const uCode = ref<any>(null);
const slideCodeRef = ref();

const form = reactive({
  phone: '',
  code: '',
});

// 样式配置 - 学习login页面的样式
const inputStyle = {
  height: '92rpx',
  background: '#FFFFFF',
  borderRadius: '100rpx',
  padding: '0 48rpx',
  fontSize: '32rpx',
};

const placeholderStyle = {
  color: '#AEAEB2',
  fontSize: '28rpx',
  fontWeight: '400',
};

const buttonStyle = {
  width: '320rpx',
  height: '84rpx',
  borderRadius: '100rpx',
  fontSize: '32rpx',
  fontWeight: '600',
};

// 表单验证
const isValid = computed(() => {
  return form.phone && /^1\d{10}$/.test(form.phone) && form.code;
});

// 验证码提示文字
const codeTip = ref('');

// 暴露方法
const emit = defineEmits(['success', 'close']);

// 打开弹窗
const open = () => {
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  emit('close');
};

// 重置表单
const reset = () => {
  form.phone = '';
  form.code = '';
  codeTip.value = '';
  if (uCode.value) {
    uCode.value.reset();
  }
};

// 获取短信验证码 - 学习login页面的逻辑
const getSmsCodeFn = async () => {
  if (!uCode.value.canGetCode) return;

  if (!form.phone || !/^1\d{10}$/.test(form.phone)) {
    uToastRef.value.show({
      message: '请输入正确的手机号',
      type: 'info',
    });
    return;
  }

  try {
    const slideParams = await slideCodeRef.value.open();
    await getSmsCode({
      mobile: form.phone,
      ...slideParams,
    });

    uToastRef.value.show({
      message: '验证码已发送',
      type: 'success',
    });
    uCode.value.start();
  } catch (error: any) {
    uCode.value.reset();
    uToastRef.value.show({
      message: error.message || '验证码发送失败',
      type: 'info',
    });
  }
};

// 处理重置按钮点击
const handleReset = () => {
  reset();
};

// 处理关闭
const handleClose = () => {
  close();
  reset();
};

// 处理确认绑定
const handleConfirm = async () => {
  if (!isValid.value) {
    uToastRef.value.show({
      message: '请输入正确的手机号和验证码',
      type: 'info',
    });
    return;
  }

  isLoading.value = true;
  try {
    // 发送绑定数据
    emit('success', {
      phone: form.phone,
      code: form.code,
    });
    close();
    reset();
  } catch (error: any) {
    uToastRef.value.show({
      message: error.message || '绑定失败',
      type: 'info',
    });
  } finally {
    isLoading.value = false;
  }
};

// 暴露组件方法
defineExpose({
  open,
  close,
  reset,
});
</script>

<template>
  <u-popup
    :show="visible"
    @close="handleClose"
    mode="bottom"
    round="12"
    :closeable="false"
    :safe-area-inset-bottom="true"
  >
    <view class="phone-binding">
      <!-- 标题 -->
      <view class="header">
        <text class="title">绑定手机号</text>
      </view>

      <!-- 提示文字 -->
      <view class="tips"> 为了更好地为您提供服务，请绑定您的手机号 </view>

      <!-- 表单 -->
      <view class="form">
        <view class="form-item">
          <u-input
            v-model="form.phone"
            type="number"
            maxlength="11"
            placeholder="请输入手机号"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            border="none"
          ></u-input>
        </view>

        <view class="form-item">
          <u-input
            v-model="form.code"
            type="number"
            maxlength="6"
            placeholder="请输入验证码"
            :customStyle="inputStyle"
            :placeholderStyle="placeholderStyle"
            border="none"
          >
            <template #suffix>
              <up-code
                ref="uCode"
                @change="codeTip = $event"
                seconds="60"
                changeText="Xs后重发"
              ></up-code>
              <view class="get-code-btn" @click="getSmsCodeFn">{{ codeTip || '获取验证码' }}</view>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 按钮组 -->
      <view class="button-group">
        <LkButton
          type="neutral"
          size="large"
          class="reset-button button"
          shape="round"
          @click="handleReset"
          :customStyle="buttonStyle"
          >重置</LkButton
        >

        <LkButton
          type="primary"
          size="large"
          class="confirm-button button"
          shape="round"
          @click="handleConfirm"
          :loading="isLoading"
          :disabled="!isValid"
          :customStyle="buttonStyle"
          >确认绑定</LkButton
        >
      </view>
    </view>
  </u-popup>

  <!-- 滑块验证码组件 -->
  <LkSlideCode ref="slideCodeRef"></LkSlideCode>
  <!-- Toast 提示组件 -->
  <LkToast ref="uToastRef"></LkToast>
</template>

<style lang="scss">
.phone-binding {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;

  .header {
    text-align: center;
    margin-bottom: 24rpx;

    .title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1d2129;
    }
  }

  .tips {
    font-size: 28rpx;
    color: #4e5969;
    line-height: 40rpx;
    margin-bottom: 32rpx;
    text-align: center;
  }

  .form {
    .form-item {
      margin-bottom: 32rpx;

      .get-code-btn {
        font-size: 30rpx;
        color: #7d4dff !important;
        cursor: pointer;
      }
    }
  }

  .button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 48rpx;
    padding: 0 32rpx;

    .button {
      width: 48% !important;
    }
  }
}
</style>
