<template>
  <view class="share-page-container">
    <!-- 1. Navigation Bar -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" @click="goBack"></uni-icons>
    </view>

    <scroll-view scroll-y class="scroll-content">
      <view class="content-wrapper">
        <!-- 2. Header Section -->
        <view class="header-section">
          <view class="logo-image-container">
            <!-- 请替换为您的logo图片路径 -->
            <image class="logo-icon" :src="appDetail?.avatarUrl" mode="aspectFit"></image>
          </view>
          <text class="main-title">{{ appDetail?.name }}</text>
          <text class="sub-title">{{ appDetail?.intro || '暂无介绍' }}</text>
        </view>

        <view class="action-buttons">
          <button
            class="btn favorites-btn"
            :class="{ favorited: addFavorites }"
            @click="handleFavorites"
          >
            <LkSvg
              v-if="!addFavorites"
              width="40rpx"
              height="40rpx"
              src="/static/chat/add-circle.svg"
              color="#fff"
            ></LkSvg>
            <view v-if="addFavorites" class="favorites-icon">
              <LkSvg
                width="40rpx"
                height="40rpx"
                src="/static/chat/check_fill.svg"
                color="#fff"
              ></LkSvg>
            </view>
            我的常用
          </button>
          <button class="btn share-btn" @click="handleShareApp">
            <LkSvg
              width="40rpx"
              height="40rpx"
              src="/static/chat/share-pg.svg"
              color="#333333"
            ></LkSvg>
            分享应用
          </button>
        </view>
        <view class="divider-line"> </view>

        <view class="history-section">
          <text class="history-title">历史会话</text>
          <view class="search-bar">
            <uni-icons type="search" color="#999999" size="18"></uni-icons>
            <input
              class="search-input"
              placeholder="请输入关键词搜索"
              placeholder-style="color:#999999"
              v-model="searchKeyword"
              @input="onSearchInput"
            />
          </view>

          <!-- 历史会话内容列表 -->
          <scroll-view class="chat-content" scroll-y @scrolltolower="loadMore">
            <view v-if="filteredChatData.length > 0">
              <view v-for="(day, index) in filteredChatData" :key="index" class="chat-day">
                <view class="chat-day-title">{{ day.timeLabel }}</view>
                <view
                  v-for="(message, msgIndex) in day.messages"
                  :key="msgIndex"
                  class="chat-message"
                  @click="chatDetail(message)"
                >
                  {{ message.title }}
                </view>
              </view>
            </view>
            <view v-else class="empty-state">
              <LkSvg
                width="280rpx"
                height="230rpx"
                src="/static/chat/empty_box.svg"
                color="#999999"
              ></LkSvg>
              <text class="empty-text">暂无使用数据</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>
    <!-- 分享弹窗 -->
    <up-overlay
      :show="showSharePopup"
      @close="showSharePopup = false"
      :custom-style="{
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }"
    >
      <view class="share-dialog">
        <view class="share-dialog-centered" @tap.stop>
          <view class="logo-image-container">
            <image class="logo-icon" :src="appDetail?.avatarUrl" mode="aspectFit"></image>
          </view>
          <text class="dalog-main-title">{{ appDetail?.name }}</text>
          <text class="dalog-sub-title">{{ formattedIntro }}</text>
          <view class="share-dialog-divider">
            <view class="share-dialog-qrcode">
              <up-qrcode
                ref="qrCodeRef"
                :size="133"
                :val="qrCodeValue"
                showLoading
                loadingText="loading..."
                @complete="onQRCodeComplete"
              ></up-qrcode>
            </view>
            <view class="share-dialog-qrcode-text">
              <text>扫描二维码，体验应用</text>
            </view>
            <image
              class="share-dialog-qrcode-logo"
              src="https://huayun-ai-obs-public.huayuntiantu.com/ffd5b4ea-f9bf-43a9-b2bd-6bb852639ba6.png"
              mode="scaleToFill"
            />
          </view>
        </view>
        <view class="share-actions-bottom-panel">
          <view class="share-options-grid">
            <view class="option-item" @click="shareTo('moments')">
              <view class="icon-wrapper">
                <LkSvg width="80rpx" height="80rpx" src="/static/chat/pyq_logo.svg"></LkSvg>
              </view>
              <text class="option-text">朋友圈</text>
            </view>
            <view class="option-item" @click="shareTo('wechat')">
              <view class="icon-wrapper">
                <LkSvg width="80rpx" height="80rpx" src="/static/chat/wx_logo.svg"></LkSvg>
              </view>
              <text class="option-text">微信好友</text>
            </view>
            <view class="option-item" @click="saveToAlbum">
              <view class="icon-wrapper">
                <LkSvg width="80rpx" height="80rpx" src="/static/chat/save_photo.svg"></LkSvg>
              </view>
              <text class="option-text">保存相册</text>
            </view>
          </view>
          <view class="actions-panel-divider"></view>
          <view class="cancel-row" @click="closeSharePopup">
            <text class="cancel-text-label">取消</text>
          </view>
        </view>
      </view>
    </up-overlay>

    <!-- Canvas for drawing share image (hidden) -->
    <canvas
      canvas-id="shareCanvas"
      id="shareCanvas"
      :style="{
        width: canvasWidthPxComputed + 'px',
        height: canvasHeightPxComputed + 'px',
        position: 'fixed', // Use fixed to ensure it's off-screen
        top: '-9999px',
        left: '-9999px',
        zIndex: -10, // ensure it's not interactive
      }"
    ></canvas>

    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, computed } from 'vue';
import LkSvg from '../../../components/svg/index.vue';
import * as chatApi from '../../../api/chat';
import * as userCommonApi from '../../../api/userCommon';
const uToastRef = ref();

import {
  generateAndSaveShareImage,
  type GenerateShareImageOptions,
} from '../../../utils/canvasShare';

interface ChatMessage {
  chatId: string;
  title: string;
  tenantAppId: string;
  appAvatarUrl: string;
  updateTime: string;
  timeLabel?: string;
  timeYmd?: string;
  timeHm?: string;
}

interface ChatGroup {
  timeLabel: string;
  messages: ChatMessage[];
}

interface AppDetail {
  id: string;
  appId: string;
  name: string;
  intro: string;
  avatarUrl: string;
  isCommonApp?: number;
}

// For canvas drawing
const { proxy } = getCurrentInstance()!;
const systemInfo = uni.getSystemInfoSync();
const dpr = systemInfo.pixelRatio;

// Define canvas dimensions in rpx (these match the visual design)
const canvasWidthRpx = 690;
const canvasHeightRpx = 824 + 60; // 824 for dialog, 60 for logo overflow above

// Computed properties to convert rpx to px for canvas style and drawing
const canvasWidthPxComputed = computed(() => uni.upx2px(canvasWidthRpx));
const canvasHeightPxComputed = computed(() => uni.upx2px(canvasHeightRpx));

// 日期处理工具 - Restored as it is used by onPageDataLoad
const dateUtil = {
  // 解析日期字符串为Date对象
  parseDate(dateStr: string): Date {
    return new Date(dateStr);
  },

  // 获取日期的开始时间（00:00:00）
  startOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  },

  // 计算两个日期之间的天数差
  diffDays(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date1.getTime() - date2.getTime());
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  },

  // 获取日期的年份
  getYear(date: Date): number {
    return date.getFullYear();
  },

  // 获取日期的月份（0-11）
  getMonth(date: Date): number {
    return date.getMonth();
  },

  // 获取日期是星期几（0-6，0表示星期日）
  getDay(date: Date): number {
    return date.getDay();
  },

  // 格式化日期为 YYYY-MM-DD
  formatYMD(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 格式化时间为 HH:MM
  formatHM(date: Date): string {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },
};

const showSharePopup = ref(false);
const addFavorites = ref(false);
const chatData = ref<ChatMessage[]>([]);
const filteredChatData = ref<ChatGroup[]>([]);
const current = ref(1);
const size = ref(20); // 每页加载20条数据
const total = ref(0); // 总数据条数
const hasMore = ref(true); // 是否有更多数据
const searchKeyword = ref(''); // 搜索关键字
const appId = ref<string | null>(null);
const appDetail = ref<AppDetail | null>(null);
const logoUrl = ref(
  'https://huayun-ai-obs-public.huayuntiantu.com/ffd5b4ea-f9bf-43a9-b2bd-6bb852639ba6.png'
);

// 二维码相关
interface QrCodeInstance {
  getDataURL: () => Promise<string>;
}

const qrCodeRef = ref<QrCodeInstance | null>(null);
const qrCodeValue = ref(''); // 默认二维码链接
const qrCodeImageUrl = ref(''); // 二维码图片URL

// 二维码生成完成回调
const onQRCodeComplete = (dataURL: string) => {
  qrCodeImageUrl.value = dataURL;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载页面数据
onMounted(() => {
  const tenantAppId = uni.getStorageSync('selectApp');
  if (tenantAppId && tenantAppId.id) {
    appId.value = tenantAppId.id;
    getAppDetail();
  }
  loadChatData();
});

// 获取应用详情
const getAppDetail = () => {
  if (!appId.value) return;

  chatApi
    .getAppDetailByAppId({
      id: appId.value,
    })
    .then((res: AppDetail) => {
      appDetail.value = res;
      console.log('res', res);
      addFavorites.value = res.isCommonApp === 1;

      // 设置二维码值为应用分享链接
      qrCodeValue.value = generateAppShareLink(res);
    });
};

// 生成应用分享链接
const generateAppShareLink = (app: AppDetail): string => {
  // 这里根据实际需求构建分享链接
  // 例如: https://huayuntiantu.com/app?id=xxx&name=xxx
  const baseUrl = 'https://huayuntiantu.com/app';
  const params = new URLSearchParams();

  if (app.id) params.append('id', app.id);
  if (app.name) params.append('name', encodeURIComponent(app.name));

  return `${baseUrl}?${params.toString()}`;
};

// 加载聊天数据
const loadChatData = async () => {
  if (!hasMore.value) return; // 如果没有更多数据，则不再加载

  try {
    const response = appId.value
      ? await chatApi.getChatList({
          appId: appId.value,
          keyword: null,
          current: current.value,
          size: size.value,
        })
      : await chatApi.getChatKeyWordList({
          appId: null,
          keyword: null,
          current: current.value,
          size: size.value,
        });

    renderData(response.records, response.total);
  } catch (error) {
    console.error('加载聊天数据失败:', error);
  }
};

// 根据关键字加载聊天数据
const loadChatKeyWordData = async () => {
  if (!hasMore.value) return;

  try {
    const response = appId.value
      ? await chatApi.getChatList({
          appId: appId.value,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        })
      : await chatApi.getChatKeyWordList({
          appId: null,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        });

    renderData(response.records, response.total);
  } catch (error) {
    console.error('加载搜索数据失败:', error);
  }
};

// 渲染数据
const renderData = (data: ChatMessage[], totalCount: number) => {
  const processedData = onPageDataLoad(data);
  total.value = totalCount;

  // 如果当前加载的数据条数大于等于总数据条数，说明没有更多数据了
  if (chatData.value.length + processedData.length >= total.value) {
    hasMore.value = false;
  }

  chatData.value = chatData.value.concat(processedData);
  filteredChatData.value = groupByTimeLabel(chatData.value);
};

// 搜索输入处理
const onSearchInput = function (e: any) {
  console.log('onSearchInput', e.detail.value);
  searchKeyword.value = e.detail.value;
  current.value = 1;
  chatData.value = [];
  hasMore.value = true; // 重置加载更多的标志

  if (searchKeyword.value) {
    loadChatKeyWordData();
  } else {
    loadChatData();
  }
};

// 加载更多数据
const loadMore = () => {
  if (hasMore.value) {
    current.value++;

    if (searchKeyword.value) {
      loadChatKeyWordData();
    } else {
      loadChatData();
    }
  }
};

// 处理页面数据时间格式化
const onPageDataLoad = (data: ChatMessage[]) => {
  const today = dateUtil.startOfDay(new Date());

  return data.map(it => {
    const updateDate = dateUtil.parseDate(it.updateTime);
    const updateDay = dateUtil.startOfDay(updateDate);
    const dayDiff = dateUtil.diffDays(today, updateDay);
    let label = '';

    if (dayDiff === 0) {
      label = '今天';
    } else if (dayDiff === 1) {
      label = '昨天';
    } else if (dayDiff <= 7) {
      // 获取今天和更新日期的星期几（1-7，其中7表示星期日）
      const todayDay = dateUtil.getDay(today) || 7;
      const updateDayOfWeek = dateUtil.getDay(updateDay) || 7;

      // 如果更新日期的星期小于今天的星期，说明在本周内
      if (updateDayOfWeek < todayDay) {
        label = '本周';
      } else {
        label = `${dateUtil.getMonth(updateDay) + 1}月`;
      }
    } else if (dateUtil.getYear(updateDay) === dateUtil.getYear(today)) {
      label = `${dateUtil.getMonth(updateDay) + 1}月`;
    } else {
      label = `${dateUtil.getYear(updateDay)}年`;
    }

    return {
      ...it,
      timeLabel: label,
      timeYmd: dateUtil.formatYMD(updateDate),
      timeHm: dateUtil.formatHM(updateDate),
    };
  });
};

// 按时间标签分组
const groupByTimeLabel = (data: ChatMessage[]) => {
  const groupedData: ChatGroup[] = [];
  data.forEach(item => {
    const existingGroup = groupedData.find(group => group.timeLabel === item.timeLabel);
    if (existingGroup) {
      existingGroup.messages.push(item);
    } else {
      groupedData.push({
        timeLabel: item.timeLabel || '',
        messages: [item],
      });
    }
  });
  return groupedData;
};

// 点击会话项跳转到聊天详情
const chatDetail = (message: ChatMessage) => {
  const defaultAppObj = uni.getStorageSync('defaultApp');
  const defaultAppId = defaultAppObj ? defaultAppObj.id : '';
  console.log('defaultAppId', defaultAppId);
  console.log('message.tenantAppId', message.tenantAppId);
  if (message.tenantAppId === defaultAppId) {
    uni.setStorageSync('oldChatId', message.chatId);
    uni.switchTab({
      url: `/pages/chat/index`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/chat/index?chatType=3&chatId=${message.chatId}&id=${message.tenantAppId}&name=${message.title}&appAvatarUrl=${message.appAvatarUrl}`,
    });
  }
};

// "我的常用"按钮点击事件处理
const handleFavorites = () => {
  addFavorites.value = !addFavorites.value;
  if (addFavorites.value) {
    userCommonApi.setCommonApp({ id: appDetail.value?.id || '' }).then(() => {
      uToastRef.value.show({
        message: '已添加到我的应用',
        type: 'success',
      });
    });
  } else {
    userCommonApi.rmCommonApp({ id: appDetail.value?.id || '' }).then(() => {
      uToastRef.value.show({
        message: '已从我的应用中移除',
        type: 'success',
      });
    });
  }
};

// "分享应用"按钮点击事件处理
const handleShareApp = () => {
  showSharePopup.value = true;

  // 确保二维码值已设置
  if (!qrCodeValue.value && appDetail.value) {
    qrCodeValue.value = generateAppShareLink(appDetail.value);
  }

  // 延迟一下，确保二维码组件已渲染
  setTimeout(() => {
    if (qrCodeRef.value && typeof qrCodeRef.value.getDataURL === 'function') {
      qrCodeRef.value
        .getDataURL()
        .then((url: string) => {
          qrCodeImageUrl.value = url;
        })
        .catch((err: any) => {
          console.error('获取二维码URL失败:', err);
        });
    }
  }, 300);
};

const closeSharePopup = () => {
  showSharePopup.value = false;
};

const shareTo = (platform: string) => {
  console.log('分享到:', platform);
  if (platform === 'moments') {
    uni.share({
      provider: 'weixin',
      scene: 'WXSceneSession',
      type: 5,
      imageUrl: 'https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/<EMAIL>',
      title: '欢迎体验uniapp',
      miniProgram: {
        id: 'gh_abcdefg',
        path: 'pages/index/index',
        type: 0,
        webUrl: 'http://uniapp.dcloud.io',
      },
      success: ret => {
        console.log('分享到朋友圈成功', JSON.stringify(ret));
        closeSharePopup();
      },
    });
  }
  // TODO: 实现具体的分享逻辑, 例如：uni.share({...})
};

const saveToAlbum = async () => {
  if (!appDetail.value) {
    uni.showToast({ title: '应用详情加载中，请稍候', icon: 'none' });
    return;
  }

  uni.showLoading({ title: '准备中...', mask: true });

  try {
    // 确保二维码已经生成
    if (!qrCodeImageUrl.value && qrCodeRef.value) {
      try {
        const qrInstance = qrCodeRef.value;
        if (qrInstance && typeof qrInstance.getDataURL === 'function') {
          qrCodeImageUrl.value = await qrInstance.getDataURL();
        }
      } catch (err) {
        console.error('获取二维码图片失败:', err);
      }
    }

    // 如果还是没有二维码图片，显示提示
    // if (!qrCodeImageUrl.value) {
    //   uni.hideLoading();
    //   uni.showToast({ title: '二维码生成失败，请重试', icon: 'none' });
    //   return;
    // }

    // 处理应用介绍文字，确保不会过长
    let introText = appDetail.value?.intro || '';
    if (introText.length > 100) {
      introText = introText.substring(0, 97) + '...';
    }

    const options: GenerateShareImageOptions = {
      proxy: proxy,
      canvasId: 'shareCanvas',
      canvasWidthPx: canvasWidthPxComputed.value,
      canvasHeightPx: canvasHeightPxComputed.value,
      dpr: dpr,
      dialogBackgroundImageUrl:
        'https://huayun-ai-obs-public.huayuntiantu.com/7f39f1f14f2d4e1df6c04404d2140807.png',
      staticAppIconPath:
        appDetail.value.avatarUrl || '/static/images/share_app_baishitong_icon.png',
      mainTitleText: appDetail.value?.name || '百事通',
      subTitleText: introText,
      scanQrPromptText: '扫描二维码，体验应用',
      bottomLogoUrl: logoUrl.value,
      qrCodePlaceholderText: '请扫码',
      qrCodeUrl: qrCodeImageUrl.value,
      onSuccessClosePopup: () => {
        showSharePopup.value = false;
      },
    };

    await generateAndSaveShareImage(options);
  } catch (error) {
    uni.hideLoading();
    uni.showToast({ title: '保存失败，请重试', icon: 'none' });
    console.error('保存到相册失败:', error);
  }
};

// 格式化应用介绍文字，确保不会超出
const formattedIntro = computed(() => {
  const intro = appDetail.value?.intro || '暂无介绍';
  if (intro.length > 100) {
    return intro.substring(0, 97) + '...';
  }
  return intro;
});
</script>

<style lang="scss" scoped>
.share-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/329fcfe0-66b5-464d-8170-460b809be551.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.nav-bar {
  padding-top: var(--status-bar-height);
  height: 88rpx;
  display: flex;
  align-items: center;
  padding-left: 30rpx; // 返回按钮的左边距
  flex-shrink: 0;
}

.scroll-content {
  flex: 1;
  overflow-y: auto;
}

.content-wrapper {
  padding: 0 40rpx 40rpx; // 左右及底部内边距
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx; // 与导航栏的间距
  margin-bottom: 50rpx;
  text-align: center;

  .logo-image-container {
    width: 132rpx;
    height: 132rpx;
    border-radius: 50%;
    background-color: #ffffff;
    border: 3px solid #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
    overflow: hidden; // 确保图片在圆形内
  }

  .logo-icon {
    width: 90rpx; // 图标实际大小
    height: 90rpx;
  }

  .main-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20rpx;
  }

  .sub-title {
    font-size: 24rpx;
    color: #606266;
    line-height: 1.5;
    max-width: 600rpx; // 控制文字宽度，使其换行
    margin: 0 32rpx;
    display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: 3; /* 限制在一个块元素显示的文本的行数 */
    -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;

  .btn {
    width: calc(50% - 15rpx); // 计算按钮宽度，15rpx为按钮间距的一半
    border-radius: 16rpx; // 圆角
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;
    box-sizing: border-box;
    border: none;
    // 移除默认的 ::after 边框 (在某些平台按钮可能会有)
    &::after {
      border: none;
    }
  }
  .favorites-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    background-color: #ffffff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .favorites-btn {
    background-color: #7d4dff;
    color: #ffffff;
    &.favorited {
      background-color: #b694ff;
    }
  }

  .share-btn {
    background-color: #f3f3f3;
    color: #1d2129;
  }
}

.history-section {
  margin-bottom: 50rpx;

  .history-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 24rpx;
    display: block;
  }

  .search-bar {
    display: flex;
    align-items: center;
    background-color: #f3f3f3;
    border-radius: 16rpx; // 圆角
    padding: 0 25rpx;
    height: 80rpx;
    margin-bottom: 30rpx;

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      margin-left: 15rpx;
      height: 100%;
      background-color: transparent; // 确保input背景透明
      border: none; // 移除默认边框
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 40rpx; // 与上方元素的间距

  .empty-icon {
    width: 280rpx;
    height: 230rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #4e5969;
  }
}

.divider-line {
  width: 100%;
  height: 1px;
  background-color: #f3f3f3;
  margin: 40rpx 0;
}

.chat-content {
  max-height: 700rpx;
  overflow-y: auto;
}

.chat-day {
  margin-bottom: 30rpx;
}

.chat-day-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #909399;
  margin-bottom: 20rpx;
}

.chat-message {
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.share-dialog {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS >= 11.2 */
}

.share-dialog-centered {
  width: 690rpx;
  height: 824rpx;
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/7f39f1f14f2d4e1df6c04404d2140807.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 160rpx;
  padding: 0;
  box-sizing: border-box;

  .logo-image-container {
    width: 132rpx;
    height: 132rpx;
    border-radius: 50%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: -60rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    margin-bottom: 0;
  }

  .logo-icon {
    width: 70rpx;
    height: 70rpx;
  }

  .dalog-main-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #303133;
    text-align: center;
    margin-top: 80rpx;
    margin-bottom: 0;
  }
  .dalog-sub-title {
    font-size: 24rpx;
    color: #606266;
    line-height: 1.5;
    max-width: 526rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 20rpx 0;
    line-height: 40rpx;
    text-align: center;
  }
  .share-dialog-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 574rpx;
    height: 498rpx;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 26rpx;
    border: 1px solid #ffffff;
    background: rgba(255, 255, 255, 0.32);
    backdrop-filter: blur(1.899999976158142px);
    .share-dialog-qrcode {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 286rpx;
      height: 286rpx;
      border-radius: 22rpx;
      background: #ffffff;
    }
    .share-dialog-qrcode-logo {
      width: 113.756rpx;
      height: 46rpx;
    }
    .share-dialog-qrcode-text {
      font-size: 24rpx;
      color: #1d2129;
      margin-top: 16rpx;
      margin-bottom: 54rpx;
    }
  }
}

.share-actions-bottom-panel {
  background-color: #ffffff;
  width: 100%;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  padding-top: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.share-options-grid {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
}

.option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;

  .icon-wrapper {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .option-icon {
    width: 60rpx;
    height: 60rpx;
  }

  .option-text {
    font-size: 24rpx;
    color: #303133;
  }
}

.actions-panel-divider {
  height: 1px;
  background-color: #e5e7eb;
  width: 100%;
}

.cancel-row {
  padding: 30rpx 0;
  text-align: center;
  width: 100%;

  .cancel-text-label {
    font-size: 32rpx;
    color: #303133;
  }
}
</style>
