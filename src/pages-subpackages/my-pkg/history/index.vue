<template>
  <view class="container safe-area-inset-bottom">
    <u-navbar
      class="my-history-navbar"
      title="对话记录"
      border
      :autoBack="true"
      placeholder
      bgColor="#f6f6fc"
    >
      <template #right>
        <view class="right-icon" @click="onSearch">
          <image
            src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/5e552e61de657442f701e1ed62a70bf6.svg"
            mode="scaleToFill"
          />
        </view>
      </template>
    </u-navbar>
    <view class="content-wrapper">
      <view class="appArea" v-if="appId">
        <!-- 详情介绍 -->
        <view class="detail-header">
          <view class="header-icon">
            <image class="icon" :src="appDetail?.avatarUrl || ''" mode="aspectFit"></image>
          </view>
          <view class="title">{{ appDetail?.name || '' }}</view>
          <view class="desc">{{ appDetail?.intro || '' }}</view>
        </view>

        <view class="line"></view>
        <!-- 历史对话标题 -->
        <view class="history-title">历史会话</view>
      </view>
      <view v-else class="appArea">
        <view class="detail-header"></view>
      </view>
      <view class="history">
        <!-- 搜索栏 -->
        <!-- <view class="search-bar">
          <u-search
            height="72rpx"
            :inputStyle="inputStyle"
            placeholder="搜索历史记录"
            :showAction="false"
            v-model="searchKeyword"
            @input="onSearchInput"
            class="search"
          ></u-search>
        </view> -->

        <!-- 聊天内容 -->
        <scroll-view
          class="chat-content"
          scroll-y
          @scrolltolower="loadMore"
          :style="getScrollViewStyle()"
        >
          <view v-for="(day, index) in filteredChatData" :key="index" class="chat-day">
            <view class="chat-day-title">{{ day.timeLabel }}</view>
            <view
              v-for="(message, msgIndex) in day.messages"
              :key="msgIndex"
              class="chat-message"
              @click="chatDetail(message)"
            >
              {{ message.title || '无标题对话' }}
            </view>
          </view>
          <view v-if="filteredChatData.length === 0" class="empty-data"> 暂无历史对话记录 </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import dayjs from '@/utils/day';
// import api from '@/api/chat.js';
// import debounce from 'lodash/debounce';
import { getAppDetail, getChatKeyWordList, getChatList } from '@/api/my-pkg/history';
import { SearchType } from '@/constants/search';

// 定义接口类型
interface ChatMessage {
  chatId: string;
  title: string;
  tenantAppId: string;
  appAvatarUrl: string;
  updateTime: string;
  timeLabel?: string;
  timeYmd?: string;
  timeHm?: string;
}

interface ChatDay {
  timeLabel: string;
  messages: ChatMessage[];
}

interface AppDetail {
  id?: string;
  name?: string;
  intro?: string;
  avatarUrl?: string;
}

// 获取路由参数
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

// 定义响应式变量
const appId = ref<string | null>(null);
const appDetail = ref<AppDetail | null>(null);
const chatData = ref<ChatMessage[]>([]);
const filteredChatData = ref<ChatDay[]>([]);
const current = ref(1);
const size = ref(20); // 每页加载20条数据
const total = ref(0); // 总数据条数
const hasMore = ref(true); // 是否有更多数据
const searchKeyword = ref(''); // 搜索关键字
const inputStyle = {
  'border-radius': '8px',
};
const myHistoryNavbarHeight = ref(44);
const systemInfo = ref<any>(null);

// 获取系统信息
onMounted(() => {
  // @ts-ignore
  systemInfo.value = uni.getSystemInfoSync();

  // 延迟获取导航栏高度，确保DOM已渲染
  setTimeout(() => {
    const query = uni.createSelectorQuery();
    query
      .select('.my-history-navbar')
      .boundingClientRect((data: any) => {
        if (data) {
          myHistoryNavbarHeight.value = data.height;
        }
      })
      .exec();
  }, 100);

  // 初始化数据
  initData();
});

/**
 * 初始化数据
 */
const initData = () => {
  // 从路由参数获取appId
  appId.value = props.id || null;
  if (appId.value) {
    fetchGetAppDetail();
  }
  loadChatData();
};

/**
 * 获取滚动视图样式
 */
const getScrollViewStyle = () => {
  const isIOS = systemInfo.value && systemInfo.value.platform === 'ios';
  const baseHeight = appId.value ? '750rpx' : 'calc(100vh - 120rpx)';

  // iOS添加额外的安全区域适配
  if (isIOS) {
    return {
      height: baseHeight,
      paddingBottom: '20px', // iOS安全区域额外空间
    };
  }

  return {
    height: baseHeight,
  };
};

/**
 * 获取应用详情
 */
const fetchGetAppDetail = () => {
  if (!appId.value) return;

  getAppDetail({
    id: appId.value,
  })
    .then((res: any) => {
      appDetail.value = res;
    })
    .catch((err: any) => {
      console.error('获取应用详情失败', err);
    });
};

/**
 * 加载聊天数据
 */
const loadChatData = async () => {
  if (!hasMore.value) return; // 如果没有更多数据，则不再加载

  try {
    const response = appId.value
      ? await getChatList({
          appId: appId.value,
          keyword: '',
          current: current.value,
          size: size.value,
        })
      : await getChatKeyWordList({
          appId: null,
          keyword: null,
          current: current.value,
          size: size.value,
        });
    renderData(response.records || [], response.total || 0);
  } catch (error) {
    // 处理错误
    console.error('加载聊天数据失败', error);
    renderData([], 0); // 出错时显示空数据
  }
};

/**
 * 根据关键词加载聊天数据
 */
const loadChatKeyWordData = async () => {
  if (!hasMore.value) return; // 如果没有更多数据，则不再加载

  try {
    const response = appId.value
      ? await getChatList({
          appId: appId.value,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        })
      : await getChatKeyWordList({
          appId: null,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        });
    renderData(response.records || [], response.total || 0);
  } catch (error) {
    // 处理错误
    console.error('加载关键词聊天数据失败', error);
    renderData([], 0); // 出错时显示空数据
  }
};

/**
 * 渲染数据并更新状态
 */
const renderData = (data: ChatMessage[], totalCount: number) => {
  const processedData = onPageDataLoad(data || []);
  total.value = totalCount; // 更新总数据条数

  // 判断是否还有更多数据
  if (chatData.value.length + processedData.length >= total.value) {
    hasMore.value = false;
  }

  chatData.value = chatData.value.concat(processedData);
  filteredChatData.value = groupByTimeLabel(chatData.value);
};

/**
 * 搜索输入处理函数（防抖）
 */
// const onSearchInput = debounce(function (e: string) {
//   searchKeyword.value = e; // 更新搜索关键字
//   current.value = 1;
//   chatData.value = [];
//   hasMore.value = true; // 重置加载更多的标志

//   if (searchKeyword.value) {
//     loadChatKeyWordData();
//   } else {
//     loadChatData();
//   }
// }, 300);

/**
 * 加载更多数据
 */
const loadMore = () => {
  if (hasMore.value) {
    current.value++;
    if (searchKeyword.value) {
      loadChatKeyWordData();
    } else {
      loadChatData();
    }
  }
};

/**
 * 处理页面数据，添加时间标签
 */
const onPageDataLoad = (data: ChatMessage[]) => {
  if (!data || data.length === 0) return [];

  const today = dayjs().startOf('day') as any;
  return data.map(it => {
    if (!it.updateTime) {
      return {
        ...it,
        timeLabel: '未知时间',
        timeYmd: '',
        timeHm: '',
      };
    }

    const ut = dayjs(it.updateTime) as any;
    const t = ut.startOf('day') as any;
    const d = today.diff(t, 'day') as any;
    let label = '';

    // 根据日期差值设置时间标签
    if (d === 0) {
      label = '今天';
    } else if (d == 1) {
      label = '昨天';
    } else if (d <= 7 && (t.day() ? t.day() : 7) < (today.day() ? today.day() : 7)) {
      label = '本周';
    } else if (t.year() === today.year()) {
      label = `${t.month() + 1}月`;
    } else {
      label = `${t.year()}年`;
    }

    return {
      ...it,
      timeLabel: label,
      timeYmd: ut.format('YYYY-MM-DD'),
      timeHm: ut.format('HH:mm'),
    };
  });
};

/**
 * 按时间标签对数据进行分组
 */
const groupByTimeLabel = (data: ChatMessage[]) => {
  const groupedData: ChatDay[] = [];
  data.forEach(item => {
    if (!item.timeLabel) return;

    const existingGroup = groupedData.find(group => group.timeLabel === item.timeLabel);
    if (existingGroup) {
      existingGroup.messages.push(item);
    } else {
      groupedData.push({
        timeLabel: item.timeLabel,
        messages: [item],
      });
    }
  });
  return groupedData;
};

/**
 * 跳转到聊天详情页
 */
const chatDetail = (message: ChatMessage) => {
  if (!message || !message.chatId) {
    // @ts-ignore
    uni.showToast({
      title: '聊天信息不完整',
      icon: 'none',
    });
    return;
  }

  try {
    // @ts-ignore
    const defaultApp = uni.getStorageSync('defaultApp') || {};
    const defaultAppId = defaultApp.id || '';
    if (message.tenantAppId === defaultAppId) {
      // @ts-ignore
      uni.setStorageSync('oldChatId', message.chatId);
      // @ts-ignore
      uni.switchTab({
        url: `/pages/chat/chat`,
      });
    } else {
      // @ts-ignore
      uni.navigateTo({
        url: `/pages/chat/index?chatType=3&chatId=${message.chatId}&id=${message.tenantAppId}&name=${encodeURIComponent(message.title || '')}&appAvatarUrl=${encodeURIComponent(message.appAvatarUrl || '')}`,
      });
    }
  } catch (error) {
    console.error('跳转聊天页面失败', error);
    // @ts-ignore
    uni.showToast({
      title: '跳转聊天页面失败',
      icon: 'none',
    });
  }
};

/**
 * 返回上一页
 */
const navBack = () => {
  // @ts-ignore
  uni.navigateBack();
};

/**
 * 解析图片URL
 */
// @ts-ignore
const resolveImageUrl = (url: string, suffix = '') => {
  // 这里可能需要根据实际环境替换成正确的实现
  return url;
};

const onSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index?activeType=' + SearchType.CHAT,
  });
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.my-history-navbar {
  .right-icon {
    width: 24 * 2rpx;
    height: 24 * 2rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  z-index: 999;

  .header-left-img {
    width: 48rpx;
    height: 48rpx;
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #f6f6fc;
  position: relative;
  width: 100%;
}

.content-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.detail-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-bottom: 16rpx;
}

.header-icon {
  width: 124rpx;
  height: 124rpx;
  margin-right: 8rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.icon {
  width: 124rpx;
  height: 124rpx;
}

.title {
  display: flex;
  align-items: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 34rpx;
  color: #303133;
  line-height: 40rpx;
  margin: 24rpx 0rpx 22rpx 0rpx;
}

.desc {
  text-align: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #606266;
  padding: 0 72rpx;
  margin-bottom: 60rpx;
}

.history {
  flex: 1;
  width: 100%;
}

.history-title {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 30rpx;
  color: #303133;
  margin: 40rpx 0rpx 26rpx 32rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  margin: 0rpx 32rpx;
}

::v-deep .u-search__content {
  border-radius: 16rpx !important;
}

.chat-content {
  flex: 1;
  width: 100%;
  padding: 0 32rpx;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

.chat-day {
  margin: 26rpx 0rpx 14rpx;
  width: 100%;
}

.chat-day-title {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  color: #909399;
  padding-left: 8rpx;
}

.chat-message {
  padding: 24rpx;
  margin: 10rpx 0rpx;
  font-size: 28rpx;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.line {
  height: 1px;
  margin: 0 32rpx;
  background-color: #eee;
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 40rpx 0;
  font-size: 28rpx;
}
</style>
