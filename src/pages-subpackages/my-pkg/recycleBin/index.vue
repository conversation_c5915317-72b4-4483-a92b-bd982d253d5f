<script setup lang="ts">
import { useSystemStore } from '@/store/systemStore';
import LkSvg from '@/components/svg/index.vue';
import { ref, computed } from 'vue';
import LkDatabaseItem from '@/components/LkDatabase/LkDatabaseItem.vue';
import LkPageList from '@/components/LkPageList/index.vue';
import { deleteRecycleBinFile, recoveryRecycleBinFile, hasParent } from '@/api/database';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
const navBack = () => {
  uni.navigateBack();
};
const searchValue = ref('');
const handleSearch = () => {
  if (lkDatabaseItemRef.value) {
    lkDatabaseItemRef.value.setSearchKeywordAndRefresh(searchValue.value);
  }
};

const placeholderStyle = {
  color: '#86909C',
  fontSize: '32rpx',
};

const systemStore = useSystemStore();
const uNavbarHeight = computed(() => systemStore.getNavBarHeight);

const optionsList = ref<any[]>([
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/23b8c65f90b28a6b6af694668aadd869.svg',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
]);

const lkDatabaseItemRef = ref<InstanceType<typeof LkDatabaseItem>>();
const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const isRecovery = ref(false);
const isDel = ref(false);
const curItem = ref<any>(null);
const isEditing = ref(false);
const clickOption = async (payload: { eventData: any; itemData: any }) => {
  const { eventData: optionsData, itemData: currentItem } = payload;
  if (optionsData.index === 0) {
    // 恢复
    console.log('恢复');
    const isHasParent = await hasParent({ id: currentItem.id });
    console.log(isHasParent);

    if (isHasParent) {
      const res = await recoveryRecycleBinFile({ id: currentItem.id });
      console.log(res);
      // 拉取空间数据并重新渲染
      lkDatabaseItemRef.value?.lkPageListRef?.refresh();
    } else {
      isRecovery.value = true;
      curItem.value = currentItem;
    }
  } else if (optionsData.index === 1) {
    // 彻底删除
    console.log('彻底删除');
    isDel.value = true;
    curItem.value = currentItem;
  }
};

const clickRecovery = async () => {
  console.log('恢复', curItem.value);
  // 显示学校数据空间popup
  if (curItem.value.bizType === 1) {
    // 显示我的数据空间popup
  } else if (curItem.value.bizType === 2) {
  }
  lkDatabasePopupRef.value?.openPopup();
  isRecovery.value = false;
};

const confirmRecovery = async (currentParentId: string) => {
  const res = await recoveryRecycleBinFile({ id: curItem.value.id, parentId: currentParentId });
  // 拉取空间数据并重新渲染
  lkDatabaseItemRef.value?.lkPageListRef?.refresh();
  isRecovery.value = false;
};

const confirmDel = async () => {
  const res = await deleteRecycleBinFile({ id: curItem.value.id });
  // 拉取空间数据并重新渲染
  lkDatabaseItemRef.value?.lkPageListRef?.refresh();
  isDel.value = false;
};

const clickEditItem = () => {
  isEditing.value = !isEditing.value;
};

const clickCheckAll = () => {
  console.log('全选');
};
</script>
<template>
  <view class="container">
    <!-- header -->
    <u-navbar>
      <template #left>
        <up-icon name="arrow-left" size="40rpx" color="#000" @tap="navBack" />
      </template>
      <template #center>
        <text class="nav-title">回收站</text>
      </template>
      <template #right>
        <LkSvg
          v-if="!isEditing"
          width="24px"
          height="24px"
          src="/static/recycleBin/editItem.svg"
          @click="clickEditItem"
        />
        <text v-else class="nav-action-text" @click="clickCheckAll">全选</text>
      </template>
    </u-navbar>
    <view class="main" :style="{ paddingTop: `${uNavbarHeight}px` }">
      <!-- search -->
      <view class="wrapSearch">
        <view class="searchLogo">
          <LkSvg
            width="24px"
            height="24px"
            src="/static/recycleBin/search.svg"
            @click="handleSearch"
          />
          <up-input
            v-model="searchValue"
            :show-action="false"
            placeholder="请输入关键词搜索"
            @search="handleSearch"
            @confirm="handleSearch"
            border="none"
            :placeholderStyle="placeholderStyle"
            clearable
          ></up-input>
        </view>
      </view>
      <!-- item -->
      <LkDatabaseItem
        ref="lkDatabaseItemRef"
        :layoutType="4"
        :bizType="'3'"
        :optionsList="optionsList"
        @clickOption="clickOption"
        :isEditing="isEditing"
      />
    </view>
  </view>
  <view v-if="isEditing" class="wrapEdit">
    <view class="editItem">
      <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_refresh.svg" />
      <view class="txt">恢复</view>
    </view>
    <view class="editItem">
      <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_del.svg" />
      <view class="txt">删除</view>
    </view>
    <view class="editItem" @click="isEditing = false">
      <LkSvg width="24px" height="24px" src="/static/recycleBin/checkAll_close.svg" />
      <view class="txt close">取消</view>
    </view>
  </view>
  <view v-if="isRecovery">
    <up-modal
      :show="isRecovery"
      @close="isRecovery = false"
      :showConfirmButton="false"
      :closeOnClickOverlay="true"
    >
      <view class="title">提示</view>
      <view class="content">
        恢复文件原所在上级文件夹或空间不存在，如需恢复，请选择恢复存储地址
      </view>
      <view class="wrapBtn">
        <LkButton type="plain" block @click="isRecovery = false">取消</LkButton>
        <LkButton type="primary" block @click="clickRecovery">选择恢复地址</LkButton>
      </view>
    </up-modal>
  </view>
  <view v-if="isDel">
    <up-modal
      :show="isDel"
      @close="isDel = false"
      :showConfirmButton="false"
      :closeOnClickOverlay="true"
    >
      <view class="title">彻底删除提示</view>
      <view class="content"> 彻底删除文件将不可找回文件，确定删除文件？ </view>
      <view class="wrapBtn">
        <LkButton type="plain" block @click="isDel = false">取消</LkButton>
        <LkButton type="danger" block @click="confirmDel">删除</LkButton>
      </view>
    </up-modal>
  </view>
  <LkDatabasePopup
    ref="lkDatabasePopupRef"
    :bizType="curItem?.bizType.toString()"
    :layoutType="4"
    @confirmRecovery="confirmRecovery"
  ></LkDatabasePopup>
</template>
<style lang="scss" scoped>
::v-deep .u-modal__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    color: #1d2129;
    font-size: 36rpx;
    font-weight: 600;
  }
  .content {
    color: #4e5969;
    font-size: 32rpx;
    margin-top: 8px;
  }
  .wrapBtn {
    display: flex;
    align-items: center;
    width: 100%;
    column-gap: 12px;
    margin-top: 24px;
    .btn {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
      margin-top: 24px;
      background-color: #7d4dff;
      border-radius: 8px;
      line-height: 40px;
      width: 100%;
      text-align: center;
    }
  }
}
.container {
  padding: 0 16px;
  background-color: white;
  min-height: 100vh;
  .nav-title {
    font-weight: 600;
    font-size: 36rpx;
    color: #1d2129;
  }
  .nav-action-text {
    font-size: 32rpx;
    color: #7d4dff;
    font-weight: 600;
  }
  .main {
  }
  .wrapSearch {
    width: 100%;
    background: #f3f3f3;
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    .searchLogo {
      display: flex;
      align-items: center;
      .u-input {
        margin-left: 4px;
      }
    }
  }
}
.wrapEdit {
  width: calc(100vw - 20px);
  position: fixed;
  bottom: 14px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  padding: 8.5px 57.83px;
  border-top: 1px solid #f4f4f4;
  border-radius: 14px;
  box-shadow:
    0px -1px 15.9px 0px rgba(0, 0, 0, 0.14),
    0px 1px 10px 0px rgba(0, 0, 0, 0.05),
    0px 2px 6.9px -1px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  .editItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    .txt {
      font-size: 26rpx;
      color: #303133;
      margin-top: 4px;
      &.close {
        color: #d54941;
      }
    }
  }
}
</style>
