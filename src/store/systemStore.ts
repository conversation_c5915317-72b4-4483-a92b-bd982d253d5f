import { defineStore } from 'pinia';

interface SystemInfo {
  showSplashAd: boolean;
  statusBarHeight: number;
  navBarHeight: number;
  menuButtonInfo: UniApp.GetMenuButtonBoundingClientRectRes;
  systemInfo: UniApp.GetSystemInfoResult;
  safeAreaInsets: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export const useSystemStore = defineStore('system', {
  state: (): SystemInfo => ({
    showSplashAd: false,
    statusBarHeight: 0,
    navBarHeight: 0,
    menuButtonInfo: {} as UniApp.GetMenuButtonBoundingClientRectRes,
    systemInfo: {} as UniApp.GetSystemInfoResult,
    safeAreaInsets: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
  }),

  getters: {
    // 获取状态栏高度
    getStatusBarHeight(): number {
      return this.statusBarHeight;
    },

    // 获取导航栏高度
    getNavBarHeight(): number {
      return this.navBarHeight;
    },

    // 获取胶囊按钮信息
    getMenuButtonInfo(): UniApp.GetMenuButtonBoundingClientRectRes {
      return this.menuButtonInfo;
    },

    // 获取系统信息
    getSystemInfo(): UniApp.GetSystemInfoResult {
      return this.systemInfo;
    },

    // 获取安全区域
    getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
      return this.safeAreaInsets;
    },

    // 获取屏幕高度
    getScreenHeight(): number {
      return this.systemInfo.screenHeight || 0;
    },

    // 获取屏幕宽度
    getScreenWidth(): number {
      return this.systemInfo.screenWidth || 0;
    },

    // 获取设备型号
    getModel(): string {
      return this.systemInfo.model || '';
    },

    // 获取设备品牌
    getBrand(): string {
      return this.systemInfo.brand || '';
    },

    // 获取系统版本
    getSystem(): string {
      return this.systemInfo.system || '';
    },

    // 获取平台
    getPlatform(): string {
      return this.systemInfo.platform || '';
    },

    getShowSplashAd(): boolean {
      return this.showSplashAd;
    },
  },

  actions: {
    // 初始化系统信息
    initSystemInfo() {
      try {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        this.systemInfo = systemInfo;
        this.statusBarHeight = systemInfo.statusBarHeight ?? 0;
        this.safeAreaInsets = systemInfo.safeAreaInsets || {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
        };

        // 根据平台计算导航栏高度
        let navBarHeight = 0;

        // #ifdef MP-WEIXIN
        // 微信小程序下获取胶囊按钮信息
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.menuButtonInfo = menuButtonInfo;
        navBarHeight =
          this.statusBarHeight +
          (menuButtonInfo.height || 32) +
          ((menuButtonInfo.top || 0) - this.statusBarHeight) * 2;
        // #endif

        // #ifdef H5
        // H5端固定导航栏高度
        navBarHeight = this.statusBarHeight + 44; // 44是导航栏的默认高度
        // #endif

        // #ifdef APP-PLUS
        // App端固定导航栏高度
        navBarHeight = this.statusBarHeight + 44; // 44是导航栏的默认高度
        // #endif

        // 如果都不匹配，使用默认值
        if (navBarHeight === 0) {
          navBarHeight = this.statusBarHeight + 44;
        }

        console.log('Platform:', systemInfo.platform);
        console.log('StatusBarHeight:', this.statusBarHeight);
        console.log('NavBarHeight:', navBarHeight);

        this.navBarHeight = navBarHeight;
      } catch (error) {
        console.error('初始化系统信息失败:', error);
        // 设置默认值
        this.navBarHeight = 44;
        this.statusBarHeight = 20;
      }
    },

    // 更新导航栏高度
    updateNavBarHeight(height: number) {
      this.navBarHeight = height;
    },

    // 更新状态栏高度
    updateStatusBarHeight(height: number) {
      this.statusBarHeight = height;
    },

    // 更新安全区域
    updateSafeAreaInsets(insets: { top: number; right: number; bottom: number; left: number }) {
      this.safeAreaInsets = insets;
    },

    // 设置广告图片
    setShowSplashAd(show: boolean) {
      this.showSplashAd = show;
    },
  },
});
