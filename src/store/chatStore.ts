import { defineStore } from 'pinia';

// 定义聊天状态类型
interface ChatState {
  hiddenContent: string;
  flowList: any[];
}

// 定义聊天状态
export const useChatStore = defineStore('chat', {
  state: (): ChatState => ({
    hiddenContent: '',
    flowList: uni.getStorageSync('flowList') || [],
  }),

  getters: {
    // 获取隐藏内容
    getHiddenContent: state => state.hiddenContent,

    // 获取流程列表
    getFlowList: state => state.flowList,
  },

  actions: {
    // 设置隐藏内容
    setHiddenContent(content: string) {
      this.hiddenContent = content;
    },

    // 移除隐藏内容
    removeHiddenContent() {
      this.hiddenContent = '';
    },

    // 设置流程列表
    setFlowList(flowList: any[]) {
      this.flowList = flowList;
      uni.setStorageSync('flowList', flowList);
    },

    // 移除流程列表
    removeFlowList() {
      this.flowList = [];
      uni.removeStorageSync('flowList');
    },
  },
});
