import type { LoginResponse, TenantInfo } from '@/types/api/database';

const getHttp = () => uni.$u.http;

// 获取登录验证码
export function getSmsCode(params: any) {
  return getHttp().post(`/huayun-ai/app/auth/sms/code`, params);
}

// 验证登录
export function getAuthValid(params: { mobile: string; code: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/valid', params);
}

// 获取验证码
export function getCaptcha(params: any) {
  return getHttp().post('/huayun-ai/app/auth/getCaptcha', params, {
    headers: {
      'content-type': 'application/json',
    },
  });
}

// 校验验证码
export function validCaptcha(params: any) {
  return getHttp().post(
    `/huayun-ai/app/auth/getCaptcha?moveLength=${params.moveLength}&ticket=${params.ticket}`,
    { params },
    {
      headers: {
        'content-type': 'application/json',
      },
    }
  );
}

// 获取租户列表
export function getTenantList(params: { accessKey: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/tenant/list', params, {
    custom: {
      toast: false,
      dontRefreshToken: true,
    },
  });
}

// 登录
export function login(params: { accessKey: string }): Promise<LoginResponse> {
  return getHttp().post('/huayun-ai/app/auth/login', params);
}

// 学校数据空间
export function getSpaceFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/folder/subListPages', params);
}
// 我的数据空间
export function getMySpaceFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/subListPage', params);
}

// 删除学校-文件/文件夹
export function mixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/folder/mixBatchDelete', params);
}

// 删除我的-文件/文件夹
export function myMixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/mixBatchDelete', params);
}

// 删除学校数据空间
export function deleteSpaceFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/delete', params);
}

// 回收站列表
export function getRecycleBinFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/page', params);
}

// 父空间是否存在
export function hasParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/hasParent', params);
}

// 回收站恢复
export function recoveryRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/recovery', params);
}

// 回收站彻底删除
export function deleteRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/delete', params);
}
