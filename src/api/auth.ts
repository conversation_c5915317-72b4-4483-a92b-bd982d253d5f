import type { LoginResponse, TenantInfo } from '@/types/api/auth';

const getHttp = () => uni.$u.http;

// 获取登录验证码
export function getSmsCode(params: any) {
  return getHttp().post(`/huayun-ai/app/auth/sms/code`, params);
}

// 验证登录
export function getAuthValid(params: { mobile: string; code: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/valid', params);
}

// 获取验证码
export function getCaptcha(params: any) {
  return getHttp().post('/huayun-ai/app/auth/getCaptcha', params, {
    headers: {
      'content-type': 'application/json',
    },
  });
}

// 校验验证码
export function validCaptcha(params: any) {
  return getHttp().post(
    `/huayun-ai/app/auth/getCaptcha?moveLength=${params.moveLength}&ticket=${params.ticket}`,
    { params },
    {
      headers: {
        'content-type': 'application/json',
      },
    }
  );
}

// 获取租户列表
export function getTenantList(params: { accessKey: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/tenant/list', params, {
    custom: {
      toast: false,
      dontRefreshToken: true,
    },
  });
}

// 登录
export function login(params: { accessKey: string }): Promise<LoginResponse> {
  return getHttp().post('/huayun-ai/app/auth/login', params);
}

// 更新密码
export function updatePassword(data: any) {
  return getHttp().post('/huayun-ai/app/auth/updatePassword', data);
}

// 账号密码登录
export function loginByPassword(params: any) {
  return getHttp().post('/huayun-ai/app/auth/tenantListByPassword', params);
}

// 重置密码
export function resetPassword(params: any) {
  return getHttp().post('/huayun-ai/app/auth/resetPwd', params);
}

// 重置密码验证码
export function resetPasswordSmsCode(params: any) {
  return getHttp().post('/huayun-ai/app/auth/resetPwdSmsCode', params);
}

// 获取租户列表
export function getTenantListByJsCode(params: { jsCode: string }): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/refresh/tenant/list', params);
}

// 绑定手机号到微信账号
export function bindPhoneToWechat(params: {
  phone: string;
  code: string;
  jsCode: string;
}): Promise<TenantInfo[]> {
  return getHttp().post('/huayun-ai/app/auth/bind/phone/wechat', params);
}
