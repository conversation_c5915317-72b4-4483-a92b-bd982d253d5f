<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import {
  getSpaceFileList,
  getMySpaceFileList,
  mixBatchDelete,
  myMixBatchDelete,
  deleteSpaceFile,
} from '@/api/database';
import { ref } from 'vue';
import FileViewer from '@/components/LkFileViewer/index.vue';
import OptionTool from './OptionTool.vue';
import OptionShare from './OptionShare.vue';
import OptionDel from './OptionDel.vue';
import LkPageList from '@/components/LkPageList/index.vue';
import NoData from './NoData.vue';
import { useUserStore } from '@/store/userStore';

const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);

const props = defineProps({
  isCreator: {
    type: Boolean,
    default: false,
  },
  isFileInfo: {
    type: Boolean,
    default: false,
  },
  pathList: {
    type: Array as () => any[],
    required: true,
    default: () => [],
  },
  bizType: {
    type: String,
    default: '1',
  },
});

const fileProps = ref({
  fileUrl: '',
  fileType: '',
});
const fileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

// 使用 LkPageList 替代原有的加载逻辑
const fetchSpaceFileList = async (params: any) => {
  console.log(params);
  return await (props.bizType === '1' ? getSpaceFileList : getMySpaceFileList)({
    ...params,
  });
};

const isNoData = ref(false);
const processData = (data: any) => {
  console.log(data);
  if (data.length === 0) {
    isNoData.value = true;
  }
  return data;
};

const optionsList = [
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/5849fcbac6a1721968c83830dfb3f622.svg',
  },
  {
    style: {
      backgroundColor: '#8B8B8B',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/8122eec722aa08581f49a2b32a27317a.svg',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
];

const optionTool = ref<InstanceType<typeof OptionTool>>();
const optionShare = ref<InstanceType<typeof OptionShare>>();
const optionDel = ref<InstanceType<typeof OptionDel>>();
const lkPageListRef = ref<InstanceType<typeof LkPageList>>();

const clickOption = (obj: any, item: any) => {
  console.log('222222', obj, item);
  if (obj.index === 0) {
    // 更多
    console.log('更多');
    optionTool?.value?.onOpen({ user: 'zhangsan', title: '222' });
  } else if (obj.index === 1) {
    // 分享
    console.log('分享');
    optionShare?.value?.onOpen(true);
  } else if (obj.index === 2) {
    // 删除
    console.log('删除');
    console.log(obj);
    optionDel?.value?.onOpen(item);
  }
};

const delCb = async (item: any) => {
  console.log('删除1111', item);
  // 删除文件/文件夹
  if (item.fileType === 1 || item.fileType === 2) {
    const res = await (props.bizType === '1' ? mixBatchDelete : myMixBatchDelete)({
      list: [{ id: item.id, fileType: item.fileType }],
    });
    console.log(res);
    // 删除空间
  } else if (item.fileType === 3) {
    const res = await deleteSpaceFile({ id: item.id });
    console.log(res);
  } else {
    console.log('未知文件类型');
  }
  // 拉取空间数据并重新渲染
  lkPageListRef.value?.refresh();
};

const clickItem = (item: any) => {
  const maxPrivilege = item.privileges?.length ? Math.max(...item.privileges) : 4;
  userStore.curPrivilege = maxPrivilege || 4;
  if (item.fileType === 3 || item.fileType === 2) {
    const navTitle = props.bizType === '1' ? item.spaceName : item.folderName;
    // 跳转子级数据空间
    uni.navigateTo({
      url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.id}&navTitle=${navTitle}&bizType=${props.bizType}`,
    });
  } else if (item.fileType === 1) {
    fileProps.value = {
      fileUrl: item.file?.fileUrl,
      fileType: item.fileType,
    };
  }
};

const formatFileSize = (bytes: number, withoutB?: boolean): string => {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + (withoutB ? sizes[i][0] : sizes[i]);
};

const emit = defineEmits(['goToMySpace']);

function goToMySpace() {
  emit('goToMySpace');
}
</script>
<template>
  <view class="databaseItem">
    <NoData
      v-if="isNoData"
      :noDataType="userInfo?.roleType === '1' || userInfo?.roleType === '3' ? '2' : '1'"
      :spaceType="props.bizType"
      @goToMySpace="goToMySpace"
    />
    <LkPageList
      ref="lkPageListRef"
      v-if="!isNoData && props.bizType === '1'"
      :fetch-method="fetchSpaceFileList"
      :extra-params="{
        parentId: '0',
        privilege: 4,
      }"
      :process-data="processData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-for="item in list" :key="item.id" class="contentItem">
          <u-swipe-action>
            <u-swipe-action-item
              :options="optionsList"
              @click="(obj: any) => clickOption(obj, item)"
            >
              <view class="swipe-action" @click="clickItem(item)">
                <view class="swipe-action__content">
                  <LkSvg
                    class="fileIcon"
                    width="84rpx"
                    height="84rpx"
                    :src="fileIcon(item)"
                    :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                  />
                  <view class="wrapTxt">
                    <view class="fileName">{{
                      item.fileType === 3 ? item.spaceName : item.fileName
                    }}</view>
                    <view class="fileInfo">
                      <text class="fileSize" v-if="item.fileType === 1">{{
                        formatFileSize(item.fileSize)
                      }}</text>
                      <view class="sign" v-if="item.fileType === 1"></view>
                      <text class="fileDate">{{ item.updateTime }}</text>
                      <text class="fileCreator">{{ item.uploader }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-swipe-action>
        </view>
      </template>
    </LkPageList>

    <LkPageList
      v-if="!isNoData && props.bizType === '2'"
      ref="lkPageListRef"
      :fetch-method="fetchSpaceFileList"
      :extra-params="{
        parentId: '0',
        privilege: 4,
      }"
      :process-data="processData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-for="item in list" :key="item.id" class="contentItem">
          <u-swipe-action>
            <u-swipe-action-item
              :options="optionsList"
              @click="(obj: any) => clickOption(obj, item)"
            >
              <view class="swipe-action" @click="clickItem(item)">
                <view class="swipe-action__content">
                  <LkSvg
                    class="fileIcon"
                    width="84rpx"
                    height="84rpx"
                    :src="fileIcon(item)"
                    :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                  />
                  <view class="wrapTxt">
                    <view class="fileName">{{
                      item.fileType === 3 ? item.spaceName : item.fileName
                    }}</view>
                    <view class="fileInfo">
                      <text class="fileSize" v-if="item.fileType === 1">{{
                        formatFileSize(item.fileSize)
                      }}</text>
                      <view class="sign" v-if="item.fileType === 1"></view>
                      <text class="fileDate">{{ item.updateTime }}</text>
                      <text class="fileCreator">{{ item.uploader }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-swipe-action>
        </view>
      </template>
    </LkPageList>
  </view>
  <FileViewer :file="fileProps" @resetFile="fileProps = { fileUrl: '', fileType: '' }" />
  <OptionTool ref="optionTool" />
  <OptionShare ref="optionShare" />
  <OptionDel ref="optionDel" @confirm="delCb" />
</template>
<style lang="scss" scoped>
.databaseItem {
  padding: 0 16px;
  height: 100%;
  .contentItem {
    margin-top: 14px;
    ::v-deep .u-swipe-action-item__right {
      .u-swipe-action-item__right__button {
        &:last-child {
          border-radius: 0 14px 14px 0;
        }
      }
    }
    .swipe-action {
      border: none;
      .swipe-action__content {
        display: flex;
        align-items: center;
        padding-top: 14.39px;
        padding-bottom: 13.61px;
        border-radius: 10px;
        border: 1px solid #f4f4f4;
        background: #fff;
        padding-left: 16px;
        padding-right: 16px;
        .fileIcon {
          flex-shrink: 0;
          margin-right: 10px;
        }
        .wrapTxt {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .fileName {
            color: #1d2129;
            font-family: 'PingFang SC';
            font-size: 32rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
          }
          .fileInfo {
            display: flex;
            align-items: center;
            margin-top: 3px;
            .fileSize {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .sign {
              width: 1px;
              height: 9px;
              background: #86909c;
              margin: 0 6px;
            }
            .fileDate {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .fileCreator {
              margin-left: auto;
              font-size: 24rpx;
              color: #4e5969;
              letter-spacing: 0.06px;
            }
          }
        }
      }
    }
  }
}
</style>
