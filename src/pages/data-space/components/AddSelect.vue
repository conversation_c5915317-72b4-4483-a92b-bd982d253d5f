<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

import LkSvg from '@/components/svg/index.vue';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

interface ModelValue {
  title?: string;
  type?: 'subAdd' | 'add';
}

const defaultToolsValue = [
  {
    name: '本地文件',
    icon: 'edit-pen',
    type: 'edit-pen',
  },
  {
    name: '微信文件',
    icon: 'weixin-circle-fill',
    type: 'weixin-circle-fill',
  },
];

const modelValue = ref<ModelValue>({});

const toolList = ref<ToolItem[]>([...defaultToolsValue]);

const tool = {
  subAdd: [
    {
      name: '我的空间',
      icon: 'qq-fill',
      type: 'qq-fill',
    },
  ],
  add: [
    {
      name: '文件夹',
      icon: 'qq-fill',
      type: 'qq-fill',
    },
  ],
};

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const onOpen = (value: ModelValue) => {
  show.value = true;
  modelValue.value = value;

  const selectedTool = tool[value?.type || 'add'];
  toolList.value = [...toolList.value, ...selectedTool] as ToolItem[];
};

const onClose = () => {
  show.value = false;
  modelValue.value = {};
  toolList.value = [...defaultToolsValue];
};

const handleClickItem = (item: ToolItem) => {
  console.log(item);
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'AddSelect' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="add-select safe-area-inset-bottom">
      <view class="add-select__header flex-center">
        <LkText bold>添加</LkText>
        <LkText type="tertiary" size="small">添加至 {{ modelValue?.title }}</LkText>
      </view>
      <view class="add-select__list">
        <template v-for="item in toolList" :key="item.type">
          <view class="add-select__list-item flex-center" @tap="handleClickItem(item)">
            <up-icon :name="item.icon" size="60rpx" />
            <LkText size="small">{{ item.name }}</LkText>
          </view>
        </template>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.add-select {
  background: #fff;
  border-radius: 28rpx 28rpx 0 0;
  min-height: 30vh;
  padding: 20rpx;
  padding-top: 30rpx;
  &__header {
    gap: 10rpx;
    flex-direction: column;
  }
  &__list {
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-item {
      flex-direction: column;
      width: 150rpx;
      gap: 10rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
    }
  }
}
</style>
