<script lang="ts" setup>
import { defineOptions, ref, defineEmits } from 'vue';
import { getBaseUrl } from '@/common/ai/url';
import { uploadToSpace } from '@/api/database';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

interface ModelValue {
  title?: string;
  type?: 'subAdd' | 'add';
  bizType?: string;
  parentId?: string;
}

const defaultToolsValue = [
  {
    name: '本地文件',
    icon: 'edit-pen',
    type: 'edit-pen',
  },
  {
    name: '微信文件',
    icon: 'weixin-circle-fill',
    type: 'weixin-circle-fill',
  },
];

const emit = defineEmits(['update:modelValue']);

const modelValue = ref<ModelValue>({});
const toolList = ref<ToolItem[]>([...defaultToolsValue]);
// 上传组件
const xeUploadRef = ref<any>(null);
const uploadOptions = ref({});
const tool = {
  subAdd: [
    {
      name: '我的空间',
      icon: 'qq-fill',
      type: 'qq-fill',
    },
  ],
  add: [
    {
      name: '文件夹',
      icon: 'qq-fill',
      type: 'qq-fill',
    },
  ],
};

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const onOpen = (value: ModelValue) => {
  show.value = true;
  modelValue.value = value;

  const selectedTool = tool[value?.type || 'add'];
  toolList.value = [...toolList.value, ...selectedTool] as ToolItem[];
};

const onClose = () => {
  show.value = false;
  modelValue.value = {};
  toolList.value = [...defaultToolsValue];
};

function uploadToServer(event: any, type: string) {
  console.log(event);
  let files = event;
  uni.showLoading({
    title: '上传中...',
  });

  files.forEach((fileItem: any, index: number) => {
    const filePath = type === 'image_url' ? fileItem : fileItem.tempFilePath;

    const filenameQueryParam =
      typeof fileItem.fileType !== 'undefined' && fileItem.fileType === 'file' && fileItem.name
        ? `?filename=${fileItem.name}`
        : '';
    const baseUrl = getBaseUrl();
    uni.uploadFile({
      url: `${baseUrl}/huayun-ai/system/file/public/upload${filenameQueryParam}`,
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      filePath: filePath,
      name: 'file',
      success: uploadResult => {
        let data;
        try {
          data = JSON.parse(uploadResult.data);
        } catch (e) {
          console.error('解析服务器响应失败:', uploadResult.data, e);
          if (index === files.length - 1) {
            uni.hideLoading();
          }
          uni.showToast({ title: '服务器响应格式错误', icon: 'none' });
          return;
        }
        if (data.code == 200) {
          console.log('上传成功:', data);
          const fileData = {
            fileUrl: data.data.fileUrl,
            fileName: data.data.fileName,
            fileKey: data.data.fileKey,
            id: data.data.id,
            fileType: data.data.fileType,
            fileSize: data.data.fileSize,
            type: type,
            uploadTime: Date.now(),
          };

          uni.uploadFile({
            url: `${baseUrl}/huayun-ai/cloud/file/upload`,
            header: {
              Authorization: uni.getStorageSync('token'),
            },
            filePath: filePath,
            name: 'file',
            formData: {
              fileKey: fileData.fileKey,
              bizType: Number(modelValue.value.bizType),
              folderId: modelValue.value.parentId,
              // 是否在审核列表顶级展示：1-是；0-否
              isAuditRoot: 1,
            },
            success: uploadResult => {
              let data;
              try {
                data = JSON.parse(uploadResult.data);
              } catch (e) {
                console.error('解析服务器响应失败:', uploadResult.data, e);
                if (index === files.length - 1) {
                  uni.hideLoading();
                }
                uni.showToast({ title: '服务器响应格式错误', icon: 'none' });
                return;
              }
              if (data.code == 200) {
                console.log('上传成功2:', data);
              } else {
                console.error('上传失败:', data);
                uni.showToast({ title: data.message || `上传失败 (${data.code})`, icon: 'none' });
              }

              if (index === files.length - 1) {
                uni.hideLoading();
              }
            },
            fail: err => {
              console.error('上传请求失败:', err);
              uni.hideLoading();
              uni.showToast({ title: '上传请求失败', icon: 'none' });
            },
          });
        } else {
          console.error('上传失败:', data);
          uni.showToast({ title: data.message || `上传失败 (${data.code})`, icon: 'none' });
        }

        if (index === files.length - 1) {
          uni.hideLoading();
        }
      },
      fail: err => {
        console.error('上传请求失败:', err);
        uni.hideLoading();
        uni.showToast({ title: '上传请求失败', icon: 'none' });
      },
    });
  });
}

function handleUploadCallback(e: any) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);
    uploadToServer(e.data, 'file');
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    uni.showToast({ title: e.data, icon: 'none' });
  }
}

const handleClickItem = (item: ToolItem) => {
  xeUploadRef.value.upload('file', {});
  console.log(item);
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'AddSelect' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="add-select safe-area-inset-bottom">
      <view class="add-select__header flex-center">
        <LkText bold>添加</LkText>
        <LkText type="tertiary" size="small">添加至 {{ modelValue?.title }}</LkText>
      </view>
      <view class="add-select__list">
        <template v-for="item in toolList" :key="item.type">
          <view class="add-select__list-item flex-center" @tap="handleClickItem(item)">
            <up-icon :name="item.icon" size="60rpx" />
            <LkText size="small">{{ item.name }}</LkText>
          </view>
        </template>
      </view>
      <!-- 如果遇到无法唤醒且报错[wxs]："module XeUpload not found"时尝试调整层级,好像是不能和popup处于同级 -->
      <xe-upload
        ref="xeUploadRef"
        :options="uploadOptions"
        @callback="handleUploadCallback"
      ></xe-upload>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.add-select {
  background: #fff;
  border-radius: 28rpx 28rpx 0 0;
  min-height: 30vh;
  padding: 20rpx;
  padding-top: 30rpx;
  &__header {
    gap: 10rpx;
    flex-direction: column;
  }
  &__list {
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-item {
      flex-direction: column;
      width: 150rpx;
      gap: 10rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
    }
  }
}
</style>
