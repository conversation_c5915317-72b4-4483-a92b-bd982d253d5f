<script lang="ts" setup>
import { defineOptions, ref, reactive } from 'vue';

import LkSvg from '@/components/svg/index.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';

const optionDelRef = ref<any>(null);

const actionOptions = reactive([
  {
    style: {
      backgroundColor: '#D54941',
      fontSize: '24rpx',
    },
    text: '删除',
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const handleClick = (obj: any) => {
  optionDelRef.value.onOpen();
};

const handleFileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const onOpen = (value: any) => {
  show.value = true;
};

const onClose = () => {
  show.value = false;
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'RecordTransfer' });
</script>

<template>
  <up-popup closeable :show="show" :round="10" @close="onClose" v-bind="$attrs">
    <view class="record">
      <view class="record__header flex-center">
        <LkText bold size="large">传输记录</LkText>
      </view>
      <scroll-view scroll-y class="record__content">
        <up-swipe-action>
          <view class="record__content-uploading">
            <view class="record__content-header">
              <LkText bold>上传中(3)</LkText>
            </view>
            <template v-for="item in 3" :key="item">
              <up-swipe-action-item
                :options="actionOptions"
                @click="handleClick"
                class="record__card"
              >
                <view class="record__body-card" @touchmove.stop>
                  <view class="record__item">
                    <view class="record__item-icon">
                      <LkSvg
                        class="fileIcon"
                        width="80rpx"
                        height="80rpx"
                        :src="handleFileIcon({})"
                        :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                      />
                    </view>
                    <view class="record__item-info">
                      <LkText type="primary" class="up-line-1">学生发展办公室.docx</LkText>
                      <view class="record__item-desc">
                        <LkText type="neutral" size="xs">386KB</LkText>
                        <up-line length="8" color="#666" direction="col" margin="0 10rpx" />
                        <LkText type="neutral" size="xs">2025-04-03</LkText>
                      </view>
                    </view>
                    <view class="record__item-tools">
                      <up-icon v-if="item % 2 === 0" name="play-circle-fill" size="50rpx" />
                      <up-icon v-else-if="item % 3 === 0" name="reload" size="50rpx" />
                      <up-icon v-else name="pause-circle-fill" size="40rpx" />
                    </view>
                  </view>
                  <up-line-progress
                    height="8rpx"
                    :percentage="30"
                    :showText="false"
                    style="margin-block: 20rpx"
                  />
                  <view class="record__body-card-footer">
                    <view class="record__body-card-footer-left">
                      <up-icon name="car" size="30rpx" />
                      <LkText type="neutral" size="xs">教务中心</LkText>
                      <LkText type="neutral" size="xs">>信息技术中心</LkText>
                    </view>
                    <view class="record__body-card-footer-right">
                      <template v-if="item % 2 === 0">
                        <LkText type="neutral" size="xs">20%</LkText>
                        <span style="padding-inline: 10rpx">·</span>
                        <LkText type="neutral" size="xs" customStyle="color: #FA9550;"
                          >上传已暂停</LkText
                        >
                      </template>
                      <LkText
                        v-else-if="item % 3 === 0"
                        type="neutral"
                        size="xs"
                        customStyle="color: #F6685D;"
                        >上传失败</LkText
                      >
                      <LkText v-else type="neutral" size="xs">20%</LkText>
                    </view>
                  </view>
                </view>
              </up-swipe-action-item>
            </template>
          </view>
          <view class="record__content-uploaded">
            <view class="record__content-header">
              <LkText bold>已上传(2)</LkText>
              <template v-for="item in 2" :key="item">
                <up-swipe-action-item
                  :options="actionOptions"
                  @click="handleClick"
                  class="record__card"
                >
                  <view class="record__body-card" @touchmove.stop>
                    <view class="record__item">
                      <view class="record__item-icon">
                        <LkSvg
                          class="fileIcon"
                          width="80rpx"
                          height="80rpx"
                          :src="handleFileIcon({})"
                          :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                        />
                      </view>
                      <view class="record__item-info">
                        <LkText type="primary" class="up-line-1">学生发展办公室.docx</LkText>
                        <view class="record__item-desc">
                          <LkText type="neutral" size="xs">386KB</LkText>
                          <up-line length="8" color="#666" direction="col" margin="0 10rpx" />
                          <LkText type="neutral" size="xs">2025-04-03</LkText>
                        </view>
                      </view>
                    </view>
                    <up-line margin="25rpx 0 15rpx 0" />
                    <view class="record__body-card-footer">
                      <view class="record__body-card-footer-left">
                        <up-icon name="car" size="30rpx" />
                        <LkText type="neutral" size="xs">教务中心</LkText>
                      </view>
                      <view class="record__body-card-footer-right">
                        <LkText type="neutral" size="xs" customStyle="color: #2BA471;"
                          >上传成功</LkText
                        >
                      </view>
                    </view>
                  </view>
                </up-swipe-action-item>
              </template>
            </view>
          </view>
        </up-swipe-action>
      </scroll-view>
    </view>
    <OptionDel ref="optionDelRef" />
  </up-popup>
</template>

<style lang="scss" scoped>
.record {
  background: #fff;
  border-radius: 28rpx;
  padding: 20rpx;
  padding-top: 0;
  ::v-deep .u-swipe-action-item__right {
    border-radius: 0 20rpx 20rpx 0;
    overflow: hidden;
    padding: 2rpx;
  }
  &__header {
    height: 50px;
    justify-content: flex-start;
  }
  &__content {
    max-height: 85vh;
    min-height: 40vh;
    &-header {
    }
    &-uploaded {
      margin-top: 30rpx;
    }
  }
  &__card {
    margin-top: 30rpx;
    border: 1rpx solid #f4f4f4;
    border-radius: 20rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
  }
  &__item {
    display: flex;
    align-items: stretch;
    gap: 20rpx;
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8rpx;
      flex: 1;
    }
    &-desc {
      display: flex;
      align-items: center;
    }
    &-tools {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
  &__body-card {
    padding: 25rpx 30rpx;
    ::v-deep .u-line-progress__line {
      background: linear-gradient(
        90deg,
        #cfd7ff 0%,
        #788df3 43.75%,
        #ed78cb 87.02%,
        #fcb97e 98.56%
      );
    }
    &-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-left {
        display: flex;
        align-items: center;
      }
      &-right {
      }
    }
  }
}
</style>
