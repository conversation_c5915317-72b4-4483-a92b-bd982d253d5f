<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

import LkSvg from '@/components/svg/index.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

interface ModelValue {
  title?: string;
  user?: string;
}

const modelValue = ref<ModelValue>({});
const optionDelRef = ref<any>(null);

const toolList = ref<ToolItem[]>([
  {
    name: '分享',
    icon: 'share-square',
    type: 'share-square',
  },
  {
    name: '移动',
    icon: 'grid',
    type: 'grid',
  },
  {
    name: '更新',
    icon: 'reload',
    type: 'reload',
  },
  {
    name: '删除',
    icon: 'trash',
    type: 'trash',
  },
]);

const linkList = ref<ToolItem[]>([
  {
    name: '重命名',
    icon: 'edit-pen',
    type: 'edit-pen',
  },
  {
    name: '思维导图',
    icon: 'share',
    type: 'share',
  },
  {
    name: '文档解读',
    icon: 'file-text',
    type: 'file-text',
  },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const onOpen = (value: ModelValue) => {
  show.value = true;
  modelValue.value = value;
};

const onClose = () => {
  show.value = false;
  modelValue.value = {};
};

const onClickItem = (item: ToolItem) => {
  if (item.type === 'trash') {
    optionDelRef.value.onOpen();
  }
};

const handleFileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Tools' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="space-popup">
      <view class="space-popup__header">
        <view class="space-popup__header-sign">
          <LkSvg
            class="fileIcon"
            width="90rpx"
            height="90rpx"
            :src="handleFileIcon({})"
            :errorSrc="`/static/fileTypeIcon/unknown.svg`"
          />
        </view>
        <view class="space-popup__header-body">
          <LkText type="primary" bold class="up-line-1">{{ modelValue?.title || '--' }}</LkText>
          <LkText type="tertiary" size="small">{{ modelValue?.user || '--' }}</LkText>
        </view>
        <view class="space-popup__header-tools"></view>
      </view>
      <up-line margin="0 0 20rpx 0" />
      <view class="space-popup__body">
        <view class="space-popup__body-tools">
          <template v-for="item in toolList" :key="item.type">
            <view class="space-popup__body-tools-item" @tap="onClickItem(item)">
              <up-icon :name="item.icon" size="40rpx" />
              <LkText size="small">{{ item.name }}</LkText>
            </view>
          </template>
        </view>
        <view class="space-popup__body-cell">
          <up-cell-group :border="false">
            <template v-for="(item, index) in linkList" :key="item.type">
              <up-cell
                :icon="item.icon"
                :title="item.name"
                :border="index !== linkList.length - 1"
              />
            </template>
          </up-cell-group>
        </view>
      </view>
    </view>
    <OptionDel ref="optionDelRef" />
  </up-popup>
</template>

<style lang="scss" scoped>
.space-popup {
  background: #f2f3f5;
  border-radius: 28rpx 28rpx 0 0;
  min-height: 40vh;
  &__header {
    display: flex;
    align-items: flex-start;
    padding: 30rpx;
    gap: 20rpx;
    &-sign {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      gap: 15rpx;
    }
    &-tools {
    }
  }
  &__body {
    padding: 0 30rpx 30rpx 30rpx;
    &-tools {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20rpx;
      &-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        gap: 10rpx;
        padding: 20rpx;
        border-radius: 28rpx;
        background: white;
      }
    }
    &-cell {
      padding: 10rpx 20rpx;
      margin-top: 20rpx;
      background-color: white;
      border-radius: 28rpx;
    }
  }
}
</style>
