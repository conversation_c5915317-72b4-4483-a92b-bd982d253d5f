<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const emit = defineEmits(['confirm', 'cancel']);
const curValue = ref<any>(null);

const onOpen = (value: any) => {
  curValue.value = value;
  console.log(value);
  show.value = true;
};

const onClose = () => {
  show.value = false;
  emit('cancel');
};

const onConfirm = () => {
  onClose();
  emit('confirm', curValue.value);
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Delete' });
</script>

<template>
  <up-popup :show="show" :round="10" mode="center" @close="onClose" v-bind="$attrs">
    <view class="delete">
      <view class="delete__body flex-center">
        <LkText bold size="xlarge">删除</LkText>
        <LkText type="secondary" size="large" style="text-align: center"
          >删除文件全部内容将进入回收站，30 天后自动彻底删除</LkText
        >
      </view>
      <view class="delete__footer">
        <LkButton type="plain" block @click="onClose" size="large">取消</LkButton>
        <LkButton type="danger" block @click="onConfirm" size="large">确定删除</LkButton>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.delete {
  background: #fff;
  border-radius: 28rpx;
  padding: 20rpx;
  padding-top: 40rpx;
  &__body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 80vw;
    gap: 20rpx;
  }
  &__footer {
    margin-top: 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 30rpx;
    padding: 20rpx;
    &-item {
      flex-direction: column;
      width: 150rpx;
      gap: 10rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
    }
  }
}
</style>
