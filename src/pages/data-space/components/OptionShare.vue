<script lang="ts" setup>
import { defineOptions, ref, type PropType } from 'vue';

import LkSvg from '@/components/svg/index.vue';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

const modelValue = ref({});

const toolList = ref<ToolItem[]>([
  {
    name: '微信好友',
    icon: 'weixin-circle-fill',
    type: 'weixin-circle-fill',
  },
  {
    name: 'QQ好友',
    icon: 'qq-fill',
    type: 'qq-fill',
  },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const onOpen = (value: any) => {
  show.value = true;
  modelValue.value = value;
};

const onClose = () => {
  show.value = false;
  modelValue.value = {};
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'Share' });
</script>

<template>
  <up-popup closeable :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="share safe-area-inset-bottom">
      <view class="share__header flex-center">
        <LkText bold>分享</LkText>
      </view>
      <view class="share__list">
        <template v-for="item in toolList" :key="item.type">
          <view class="share__list-item flex-center">
            <up-icon :name="item.icon" size="60rpx" />
            <LkText size="small">{{ item.name }}</LkText>
          </view>
        </template>
      </view>
    </view>
  </up-popup>
</template>

<style lang="scss" scoped>
.share {
  background: #fff;
  border-radius: 28rpx 28rpx 0 0;
  min-height: 25vh;
  padding: 20rpx;
  padding-top: 30rpx;
  &__header {
  }
  &__list {
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    &-item {
      flex-direction: column;
      width: 150rpx;
      gap: 10rpx;
      padding: 20rpx;
      border-radius: 28rpx;
      background: white;
    }
  }
}
</style>
