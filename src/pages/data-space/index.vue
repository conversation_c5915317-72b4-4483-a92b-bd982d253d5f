<script setup lang="ts">
// 使用Pinia store
import { useUserStore } from '@/store/userStore';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';
import { ref, onMounted } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import NoData from './components/NoData.vue';
import DatabaseItem from './components/DatabaseItem.vue';
import AddSelect from '@/pages/data-space/components/AddSelect.vue';
import RecordTransfer from '@/pages/data-space/components/RecordTransfer.vue';

const mainStore = useUserStore();

const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();

const curTab = ref('1');
const addSelectRef = ref<any>(null);
const recordTransferRef = ref<any>(null);

const clickPopup = () => {
  // 弹出LkDatabase
  lkDatabasePopupRef.value?.openPopup();
};

// 切换tab
const handleClickTab = (type: string) => {
  console.log(type);
  curTab.value = type;
};
// 点击+号
const handleAddSpace = () => {
  addSelectRef.value.onOpen({
    title: curTab.value === '1' ? '学校数据空间' : '我的数据空间',
    type: curTab.value === '1' ? 'subAdd' : 'add',
  });
};

const onWatchTransferRecord = () => {
  recordTransferRef.value.onOpen();
};

const isModal = ref(false);
const clickQuestion = () => {
  console.log('clickQuestion');
  isModal.value = true;
};

const clickSearch = () => {
  console.log('跳转搜索页面');
  uni.navigateTo({
    url: '/pages/search/index',
  });
};
</script>
<template>
  <view class="container">
    <view class="header">
      <view class="title" @click="clickPopup">数据空间(点我打开popup)</view>
      <view class="tool">
        <LkSvg
          width="24px"
          height="24px"
          src="/static/database/question.svg"
          @tap="clickQuestion"
        />
        <LkSvg
          width="24px"
          height="24px"
          src="/static/database/transfer.svg"
          @tap="onWatchTransferRecord"
        />
        <LkSvg width="24px" height="24px" src="/static/database/search.svg" @tap="clickSearch" />
      </view>
    </view>
    <view class="wrapTab">
      <view class="wrapTxt" :class="{ active: curTab === '1' }" @click="handleClickTab('1')">
        <view class="txt">学校数据空间</view>
      </view>
      <view class="wrapTxt" :class="{ active: curTab === '2' }" @click="handleClickTab('2')">
        <view class="txt">我的数据空间</view>
      </view>
    </view>
    <view class="content">
      <!-- 根级数据空间组件 -->
      <DatabaseItem :bizType="curTab" @goToMySpace="curTab = '2'" />
    </view>
    <!-- +号 -->
    <view class="addSpace" @tap="handleAddSpace" v-if="curTab === '2'">
      <LkSvg width="24px" height="24px" src="/static/database/addSpace.svg" />
    </view>
  </view>
  <LkDatabasePopup ref="lkDatabasePopupRef" :bizType="curTab" :layoutType="2" />
  <AddSelect ref="addSelectRef" :bizType="curTab" parentId="0" />
  <up-modal
    :show="isModal"
    :showConfirmButton="false"
    :closeOnClickOverlay="true"
    @close="isModal = false"
  >
    <view class="title">提示</view>
    <view class="content"
      >学校数据空间上传文件开启了审核，审核通过后您上传的文件将在学校数据空间可见，暂支持在pc端查看上传文件的审核状态。</view
    >
    <view class="btn" @click="isModal = false">好的</view>
  </up-modal>
  <RecordTransfer ref="recordTransferRef" />
</template>
<style lang="scss" scoped>
@import 'uview-plus/theme.scss';
::v-deep .u-modal__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    color: #1d2129;
    font-size: 36rpx;
    font-weight: 600;
  }
  .content {
    color: #4e5969;
    font-size: 32rpx;
    margin-top: 8px;
  }
  .btn {
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    margin-top: 24px;
    background-color: #7d4dff;
    border-radius: 8px;
    line-height: 40px;
    width: 100%;
    text-align: center;
  }
}
.container {
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/8ea290b68afdbb3817d17878d41f60c5.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  min-height: 100%;
  height: 100%;
  padding-top: 14px;
  display: flex;
  flex-direction: column;

  .header {
    padding: 0 16px;
    padding-top: var(--status-bar-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      color: #0f0f0f;
      font-family: 'Alibaba PuHuiTi 2.0';
      font-size: 40rpx;
      font-weight: 700;
    }
    .tool {
      column-gap: 14px;
      display: flex;
    }
  }
  .wrapTab {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    column-gap: 32px;
    .wrapTxt {
      padding-top: 4rpx;
      padding-right: 12rpx;
      &.active {
        font-weight: 600;
        background: url('/static/database/txtBg.svg') no-repeat right top / 32rpx;
      }
      .txt {
        color: #1d2129;
        font-size: 36rpx;
      }
    }
  }
  .content {
    flex: 1;
    background-color: #fff;
    border-radius: 16px 16px 0px 0px;
    background: #fff;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.04);
    height: 100%;
    overflow-y: auto;
  }
  .addSpace {
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
    box-shadow:
      0px 3px 14px 2px rgba(0, 0, 0, 0.05),
      0px 8px 10px 1px rgba(0, 0, 0, 0.06),
      0px 5px 5px -3px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 10vh;
    right: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
