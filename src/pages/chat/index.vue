<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useChatStore } from '@/store/chatStore';
import {
  initChatItem,
  updateChatItem,
  removeChatItem,
  updateChatTitle,
  getParseResult,
  getAppContextDetail,
  chatSearchDataset,
} from '@/api/chat';
import dayjs from '../../utils/day';
import ai from '@/common/ai';
import { respDims } from '@/utils/respDims';
import MessageInput from '@/components/chat/MessageInput.vue';
import MessageList from '@/components/chat/MessageList.vue';
import ChatFlowStep from '@/components/workflow/ChatFlowStep.vue';
import { HeaderNav } from '@/components/LkNavBar';
import { nanoid } from '@/utils/nanoid/index';
import LkSvg from '@/components/svg/index.vue';
import { onLoad } from '@dcloudio/uni-app';

// 定义一个兼容的 findLastIndex 方法，在不支持原生方法的浏览器中使用
function findLastIndexCompat(array, predicate) {
  for (let i = array.length - 1; i >= 0; i--) {
    if (predicate(array[i], i, array)) {
      return i;
    }
  }
  return -1;
}

// 状态管理
const chatStore = useChatStore();
const route = useRoute();

// 响应式状态
const quickTitle = ref(null);
const innerChatId = ref('');
const abortSignal = ref(null);
const messages = ref([]);
const scrollIntoView = ref(null);
const resultTitle = ref('');
const tenantData = ref({});
const sideBarMessage = ref({});
const uploadLimit = ref({});
const waitForInit = ref(false);
const selectedFlowStep = ref(null);
const welcomeText = ref('');
const appAvatarUrl = ref('');
const otherTenant = ref(null);
const messageInputRef = ref(null);
const appTitle = ref('');
const isResponseLoading = ref(false);
const isParsingBackground = ref(false);
const isParsingFiles = ref(false);
const finalAppId = ref('');

// 计算属性
const chatting = computed(() => {
  return messages.value.length > 0 && messages.value[messages.value.length - 1].status !== 'finish';
});

// 获取URL参数
// 在安卓APP中useRoute可能会返回undefined，改用onLoad生命周期获取参数
const chatId = ref('');
const chatType = ref('');
const chatSubjectId = ref('');

// 初始化数据
watch(
  () => chatId.value,
  val => {
    if (val !== innerChatId.value) {
      init();
    }
  },
  { immediate: true }
);
watch(
  () => messages.value,
  val => {
    console.log('66666messages', val);
  }
);
// 初始化方法
function init() {
  stopChat();
  if (messageInputRef.value && typeof messageInputRef.value.reset === 'function') {
    messageInputRef.value.reset();
  }
  innerChatId.value = chatId.value;
  messages.value = [];
  if (tenantData.value.id) {
    waitForInit.value = false;
    JSON.parse(uni.getStorageSync('userInfo')).account && getInitChatItem();
  } else {
    waitForInit.value = true;
  }
}

// 获取租户数据
function fetchTenantData() {
  tenantData.value = {
    id: uni.getStorageSync('selectApp').id,
  };
  init();
}

// 在页面加载时获取参数
onLoad(options => {
  chatId.value = options.chatId || '';
  chatType.value = options.chatType || '';
  chatSubjectId.value = options.chatSubjectId || '';
});

// 初始化聊天记录
async function getInitChatItem(scroll = true) {
  return initChatItem({
    tenantAppId: tenantData.value.id,
    chatId: chatId.value || '',
  }).then(async res => {
    welcomeText.value = res.app?.chatConfig?.welcomeText;
    appAvatarUrl.value = res.appAvatarUrl;
    finalAppId.value = res.finalAppId;
    appTitle.value = res.app?.name;
    uploadLimit.value = res.app.chatConfig.fileSelectConfig || {
      canSelectImg: false,
      canSelectFile: false,
      maxFiles: 0,
    };

    // 处理历史记录数据
    messages.value = res.history.map(it => {
      return {
        ...it,
        dataId: it.dataId,
        status: 'finish',
        createTimeValue: dayjs(it.createTime).valueOf(),
        chatAppAvatarUrl: it.chatAppAvatarUrl,
        obj: it.obj,
        feedbackType: it.feedbackType,
        customFeedback: it.customFeedback,
        value: it.value,
      };
    });

    // 确保对话是一问一答的形式
    const formattedMessages = [];
    for (let i = 0; i < messages.value.length; i++) {
      const currentMessage = messages.value[i];
      formattedMessages.push(currentMessage);
      if (
        currentMessage.obj === 'Human' &&
        i + 1 < messages.value.length &&
        messages.value[i + 1].obj === 'AI'
      ) {
        formattedMessages.push(messages.value[i + 1]);
        i++; // 跳过下一个AI回复
      }
    }

    messages.value = formattedMessages;

    if (scroll) {
      scrollToBottom(200);
      scrollToBottom(1000);
    }
  });
}

// 侧边栏数据处理
function sideBarData(newData) {
  if (newData && newData.type == 999) {
    refeshQuickBar();
    quickStep();
  } else {
    sideBarMessage.value = newData;
    refeshQuickBar();
  }
}

// 刷新快速操作栏
function refeshQuickBar() {
  let flowList = uni.getStorageSync('flowList');
  if (flowList.length > 0) {
    let minSortItem = flowList.reduce((minItem, currentItem) => {
      return currentItem.sort < minItem.sort ? currentItem : minItem;
    });
    quickTitle.value = minSortItem.quickTitle ? minSortItem.quickTitle : null;
  } else {
    quickTitle.value = null;
  }
}

// 快速步骤操作
function quickStep() {
  let flowList = uni.getStorageSync('flowList');
  let step = {};
  if (flowList.length > 0) {
    step = flowList.filter(it => it.quickTitle == quickTitle.value)[0];
  }
  if (step.inputContent || step.proContent) {
    step.timestamp = new Date().getTime();
    otherTenant.value = null;
    selectedFlowStep.value = step;
  } else {
    otherTenant.value = step;
    clearOtherTenant(true);
  }
}

// 清除其他租户
function clearOtherTenant(value) {
  let flowList = uni.getStorageSync('flowList');
  if (flowList.length > 0 && value) {
    chatStore.setFlowList(flowList.filter(it => it.sort != otherTenant.value.sort));
  } else {
    chatStore.removeFlowList();
    otherTenant.value = null;
  }
  quickTitle.value = null;
}

// 滚动到底部
function scrollToBottom(delay = 20) {
  setTimeout(() => {
    scrollIntoView.value =
      scrollIntoView.value === 'scroll-bottom1' ? 'scroll-bottom2' : 'scroll-bottom1';
  }, delay);
}

// 设置输入内容
function setInput(input) {
  if (messageInputRef.value && typeof messageInputRef.value.setInput === 'function') {
    messageInputRef.value.setInput(input);
  } else {
    console.log('无法设置输入内容：messageInputRef.value.setInput不是函数');
  }
}

// 更新聊天内容
function updateContent(val) {
  startChat({
    content: val,
  });
}

// 删除消息
function onDeleteMessage(messageToDelete) {
  const indexToDelete = messages.value.findIndex(msg => msg.dataId === messageToDelete.dataId);

  if (indexToDelete === -1) {
    console.warn('Message to delete not found in local list.');
    return;
  }

  const idsToMarkForDeletion = new Set();
  idsToMarkForDeletion.add(messageToDelete.dataId);

  // Check if the message to delete is part of a Human-AI pair
  if (messageToDelete.obj === ai.ChatRoleEnum.human) {
    const nextMessageIndex = indexToDelete + 1;
    if (
      nextMessageIndex < messages.value.length &&
      messages.value[nextMessageIndex].obj === ai.ChatRoleEnum.ai
    ) {
      idsToMarkForDeletion.add(messages.value[nextMessageIndex].dataId);
      if (messages.value[nextMessageIndex].status !== 'finish') {
        stopChat('cancel');
      }
    }
  } else if (messageToDelete.obj === ai.ChatRoleEnum.ai) {
    if (messageToDelete.status !== 'finish') {
      stopChat('cancel');
    }
    const prevMessageIndex = indexToDelete - 1;
    if (prevMessageIndex >= 0 && messages.value[prevMessageIndex].obj === ai.ChatRoleEnum.human) {
      idsToMarkForDeletion.add(messages.value[prevMessageIndex].dataId);
    }
  }

  if (idsToMarkForDeletion.size === 0) {
    return; // Should not happen if messageToDelete.dataId was added
  }

  // Call API to remove messages from server
  if (innerChatId.value) {
    // Only attempt server deletion if there's a chat session ID
    idsToMarkForDeletion.forEach(id => {
      removeChatItem({
        chatId: innerChatId.value,
        contentId: id, // API expects contentId for the message
        tenantAppId: tenantData.value.id,
      })
        .then(() => {
          console.log(`Message ${id} removed from server.`);
        })
        .catch(error => {
          console.error(`Error removing message ${id} from server:`, error);
          // Optionally, notify user or handle error (e.g., revert local deletion if critical)
        });
    });
  } else {
    console.warn(
      'No innerChatId, skipping server deletion. Messages will be removed locally only.'
    );
  }

  // Update local messages list
  messages.value = messages.value.filter(msg => !idsToMarkForDeletion.has(msg.dataId));
}

// 停止聊天
function stopChat(reason) {
  if (abortSignal.value) {
    try {
      // 确保传递字符串参数
      abortSignal.value.abort(reason || 'user_canceled');
      abortSignal.value = null;
    } catch (error) {
      console.error('停止聊天时发生错误:', error);
    }
  } else {
    console.log('没有活动的聊天信号可以停止');
  }
}

// 开始聊天
async function startChat(input) {
  if (chatting.value) {
    return Promise.reject();
  }

  scrollToBottom();

  const content = input.content;
  const uploadList = input.uploadList || [];
  const saveChatId = chatId.value;
  const isNewChat = !innerChatId.value;
  const currentChatId = innerChatId.value || (await nanoid());
  abortSignal.value?.abort();
  const signal = ai.AbortSignal();
  abortSignal.value = signal;
  const humanDataId = await nanoid();
  const aiDataId = await nanoid();
  const currentTime = dayjs().valueOf(); // 获取当前时间戳

  console.log('uploadListuploadList', uploadList);

  const newMessages = [
    ...messages.value,
    {
      dataId: humanDataId,
      obj: ai.ChatRoleEnum.human,
      chatAppId: chatId.value,
      isLike: 0,
      value: [
        ...(uploadList
          ?.filter(item => item.type === 'file')
          .map(file => ({
            type: 'file',
            file: {
              type: 'file',
              name: file.fileName || '',
              size: file.fileSize || 0,
              fileType: file.fileType || '',
              url:
                file.fileUrl && file.fileName
                  ? `${file.fileUrl}?filename=${file.fileName}`
                  : file.fileUrl || '',
              fileId: file.fileKey || '',
            },
          })) || []),
        ...(uploadList
          ?.filter(item => item.type === 'image_url')
          .map(image => ({
            type: 'file',
            file: {
              type: 'image',
              url:
                image.fileUrl && image.fileName
                  ? `${image.fileUrl}?filename=${image.fileName}`
                  : image.fileUrl || '',
            },
          })) || []),
        ...(chatStore.hiddenContent
          ? [
              {
                type: 'prompt',
                prompt: {
                  content: chatStore.hiddenContent,
                },
              },
            ]
          : []),
        ...(content
          ? [
              {
                type: 'text',
                text: {
                  content: content,
                },
              },
            ]
          : []),
      ],
      status: 'finish',
      createTimeValue: currentTime, // 添加 createTimeValue
    },
    {
      dataId: aiDataId,
      obj: ai.ChatRoleEnum.ai,
      chatAppId: chatId.value,
      isLike: 0,
      value: [], // 修改为空数组以支持新的onMessage逻辑
      status: 'loading',
      createTimeValue: currentTime + 1, // AI消息时间略晚于用户消息，确保顺序
    },
  ];

  console.log('newMessages', newMessages);

  messages.value = newMessages;

  const onMessage = ({ text = '', status, name, event, reasoningText }) => {
    const aiMessage = messages.value.find(it => it.dataId === aiDataId);
    if (!aiMessage) {
      signal.abort('cancel'); // 使用startChat作用域内的signal
      return;
    }

    if (event === 'answer' && reasoningText) {
      const lastValue = aiMessage.value[aiMessage.value.length - 1];
      if (lastValue?.type === 'reasoning') {
        lastValue.reasoning.content += reasoningText;
      } else {
        aiMessage.value.push({
          type: 'reasoning',
          reasoning: {
            content: reasoningText,
          },
        });
      }
      isResponseLoading.value = true;
    }
    if ((event === 'answer' || event === 'fastAnswer') && text) {
      const lastValue = aiMessage.value[aiMessage.value.length - 1];
      if (lastValue?.type === 'text') {
        lastValue.text.content += text;
      } else {
        aiMessage.value.push({
          type: 'text',
          text: {
            content: text,
          },
        });
      }
      isResponseLoading.value = true;
    }

    if (name) {
      aiMessage.status = status;
      aiMessage.moduleName = name;
    }

    if (!reasoningText) {
      isResponseLoading.value = false;
    }

    scrollToBottom();
  };

  const humanIndex = findLastIndexCompat(messages.value, it => it.dataId === humanDataId);
  const humanMessage = messages.value[humanIndex];

  uploadList.forEach(item => {
    item.fileUrl += `?filename=${item.fileName}`;
  });

  // 定义 fileKeys 数组
  const fileKeys = uploadList.map(item => item.fileKey);

  let uploadListArray = [];
  if (uploadList.length > 0) {
    uploadListArray = uploadList.map(item => {
      console.log(
        'item》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉',
        item
      );
      if (item.type === 'image_url') {
        return {
          type: 'image_url',
          image_url: {
            url: item.fileUrl,
          },
        };
      } else if (item.type === 'file') {
        return {
          type: 'file_url',
          name: item.fileName,
          url: item.fileUrl,
          fileId: item.fileKey,
        };
      }
    });
  }

  let tempContentArray = [];

  // 背景知识解析文件内容
  let quotedRef = [];
  let searchSelectedRef = [];
  isParsingBackground.value = true;
  try {
    const appContextDetail = await getAppContextDetail({
      tenantAppId: uni.getStorageSync('selectApp').id,
    }).catch(error => {
      console.error('获取应用上下文失败:', error);
      return null;
    });

    if (appContextDetail?.files?.length) {
      const validFiles = appContextDetail.files.filter(item => item.authority !== 'Invalid');
      quotedRef = await Promise.all(
        validFiles.map(async item => {
          try {
            const res = await getParseResult({ fileKey: item.fileKey });
            return {
              fileContent: res.fileContent,
              fileName: item.fileName,
            };
          } catch (error) {
            console.error('解析文件失败:', error);
            return { fileContent: '', fileName: '' };
          }
        })
      );
      // 过滤掉解析失败的文件
      quotedRef = quotedRef.filter(item => item.fileContent && item.fileName);
    }

    if (appContextDetail?.spaces?.length) {
      const validSpaces = appContextDetail.spaces.filter(item => item.authority !== 'Invalid');
      if (validSpaces.length) {
        try {
          searchSelectedRef =
            (await chatSearchDataset({
              messages: [
                {
                  dataId: humanDataId,
                  content: content,
                  role: 'user',
                },
              ],
              spaceIds: validSpaces.map(item => item.spaceId),
            })) || [];
        } catch (error) {
          console.error('搜索数据集失败:', error);
          searchSelectedRef = [];
        }
      }
    }
  } catch (error) {
    console.error('背景知识解析失败:', error);
  }
  isParsingBackground.value = false;
  const hiddenContent = chatStore.hiddenContent;

  if (hiddenContent) {
    tempContentArray.push({
      type: 'prompt',
      content: hiddenContent,
    });
    chatStore.removeHiddenContent();
  }

  if (uploadListArray.length + tempContentArray.length > 0) {
    tempContentArray = uploadListArray.concat(tempContentArray);
    tempContentArray.push({
      type: 'text',
      text: content,
    });
  }
  const targetMessages = messages.value[messages.value.length - 2].value;
  let chatAppId = uni.getStorageSync('selectApp').id;
  if (
    otherTenant.value &&
    otherTenant.value.tenantAppId &&
    Number(otherTenant.value.tenantAppId) > 0
  ) {
    chatAppId = otherTenant.value.tenantAppId;
    let flowList = uni.getStorageSync('flowList');
    if (flowList.length > 0) {
      chatStore.setFlowList(flowList.filter(it => it.sort != otherTenant.value.sort));
    }
    otherTenant.value = null;
    refeshQuickBar();
  }
  const requestData = {
    chatAppId: chatAppId,
    value: JSON.stringify(targetMessages),
    content: input.content,
    fileKeys: fileKeys,
    messages: [
      {
        dataId: humanDataId,
        role: 'user',
        content: tempContentArray.length > 0 ? tempContentArray : input.content,
      },
    ],
    responseChatItemId: aiDataId,
    variables: {
      cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
    tenantAppId: uni.getStorageSync('selectApp').id,
    chatId: currentChatId,
    detail: true,
    stream: true,
  };

  try {
    console.log(
      'requestData》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉》〉',
      requestData
    );
    const fetchResult = await ai.fetch({
      url: '/huayun-ai/app/chat/completions',
      data: requestData,
      chatType: chatType.value,
      abortSignal: signal,
      onMessage: event => {
        onMessage(event);
      },
    });

    abortSignal.value = null;

    const aiIndex = findLastIndexCompat(messages.value, it => it.dataId === aiDataId);
    if (signal.reason === 'cancel' || humanIndex < 0 || aiIndex < 0) {
      aiIndex >= 0 && messages.value.splice(aiIndex, 1);
      humanIndex >= 0 && messages.value.splice(humanIndex, 1);
      return;
    }

    const aiMessage = messages.value[aiIndex];
    console.log('aiMessageaiMessage', aiMessage);

    // 获取文本内容
    const textContent = aiMessage.value.find(item => item.type === 'text')?.text?.content || '';

    if (!textContent) {
      messages.value.splice(aiIndex, 1);
      messages.value.splice(humanIndex, 1);
      if (messageInputRef.value && typeof messageInputRef.value.setInput === 'function') {
        messageInputRef.value.setInput(humanMessage.value[0]?.text?.content);
      } else {
        // 备选方案：在引用无效时临时存储内容
        console.log('无法设置输入内容：messageInputRef.value.setInput不是函数');
      }
      return;
    }

    aiMessage.status = 'finish';
    if (isNewChat) {
      if (chatId.value === saveChatId) {
        innerChatId.value = currentChatId;
        chatOnce(humanMessage, aiMessage, currentChatId);
      }
    }

    const responseData = uni.getStorageSync('responseData');
    uni.removeStorageSync('responseData');

    await updateChatItem({
      dataId: aiMessage.dataId,
      content: textContent,
      responseData: JSON.stringify(responseData),
      value: JSON.stringify(aiMessage.value),
    });
  } catch (error) {
    console.error('请求出错:', error);
  }
}

// 更新消息
function onUpdateMessage(message) {
  updateChatItem(message).then(() => {
    const found = messages.value.find(it => it.dataId == message.dataId);
    found && Object.assign(found, message);
  });
}

// 重试消息
function onRetryMessage(message) {
  stopChat('cancel');

  let index = messages.value.findIndex(it => it.dataId == message.dataId);
  while (index >= 0 && messages.value[index].obj == ai.ChatRoleEnum.ai) {
    index--;
  }

  const processMessage = messageValue => {
    return messageValue.reduce(
      (acc, item) => {
        const { type, text, file, prompt } = item;

        if (type === 'text') {
          acc.content += message.content || text.content;
        } else if (type === 'file') {
          if (file.type === 'image') {
            acc.uploadList.push({
              type: 'image_url',
              fileUrl: file.url,
            });
          } else if (file.type === 'file') {
            acc.uploadList.push({
              type: 'file_url',
              fileName: file.name,
              fileUrl: file.url.split('?')[0],
              fileKey: file.fileId,
            });
          }
        } else if (type === 'prompt') {
          acc.content += prompt.content;
        }

        return acc;
      },
      {
        content: '',
        uploadList: [],
      }
    );
  };

  const { content, uploadList } =
    message.obj === ai.ChatRoleEnum.human
      ? processMessage(message.value)
      : processMessage(messages.value[index].value);

  if (index >= 0) {
    const humanIndex = index;
    const dataIds = messages.value
      .slice(humanIndex, humanIndex + 2)
      .filter(it => it.dataId)
      .map(it => it.dataId);
    dataIds.forEach(id => {
      removeChatItem({
        chatId: innerChatId.value,
        contentId: id,
        tenantAppId: tenantData.value.id,
      });
    });
    messages.value.splice(humanIndex, 2);
  }

  if (index < 0) {
    return;
  }

  startChat({
    content,
    uploadList,
  });
}

// 一次性聊天
function chatOnce(humanMessage, aiMessage, chatId) {
  const titlePromptTemplate = `
  目标：
  -通过一轮对话内容生成一个简短的标题。

  回答要求：
  - 标题应该与对话内容一致，且与对话内容相关。
  - 标题不带标点符号。
  - 标题不带任何解释性前缀。
  - 标题长度少于20个字。

  下面是一个对话内容：

  问题:'''{{question}}'''

  回答:'''{{answer}}'''
  `;

  const signal = ai.AbortSignal();
  let fetchPromise;

  try {
    fetchPromise = ai.fetch({
      url: '/huayun-ai/client/chat/once',
      data: {
        messages: [
          {
            role: 'user',
            content: titlePromptTemplate
              .replace(
                '{{question}}',
                humanMessage?.value
                  ?.map(it => (it.type === 'text' ? it.text.content : ''))
                  .join('') || ''
              )
              .replace(
                '{{answer}}',
                aiMessage?.value?.map(it => (it.type === 'text' ? it.text.content : '')).join('') ||
                  ''
              ),
          },
        ],
        chatId: chatId,
        tenantAppId: uni.getStorageSync('selectApp').id,
        detail: true,
        stream: true,
        variables: {
          cTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      },
      onMessage: event => {
        console.log('onMessage', event);
        // 处理接收到的消息
        const messageData = event.text;
        resultTitle.value = resultTitle.value + (messageData || '');
      },
      abortSignal: signal,
    });
  } catch (error) {
    console.error('chatOnce请求初始化错误:', error);
    fetchPromise = Promise.reject(error);
  }

  fetchPromise
    .catch(error => {
      console.error('chatOnce请求出错:', error);
    })
    .finally(() => {
      try {
        updateChatTitle({
          chatId: innerChatId.value,
          title: resultTitle.value || '新对话',
        });
      } catch (error) {
        console.error('更新对话标题失败:', error);
      }
    });
}

// 生命周期钩子
onMounted(() => {
  fetchTenantData();
  nextTick(() => {
    refeshQuickBar();
  });
});

onUnmounted(() => {
  stopChat('cancel');
});

// 处理导航栏事件
const handleBack = () => {
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index',
      });
    },
  });
};

const handleRefresh = () => {
  // 刷新聊天记录
  if (innerChatId.value) {
    init();
  }
};

const handleShare = () => {
  uni.navigateTo({
    url: '/pages-subpackages/chat-pkg/share/index',
    params: {
      tenantAppId: uni.getStorageSync('selectApp').id,
    },
  });
};
const newChat = () => {
  if (JSON.parse(uni.getStorageSync('userInfo')).account) {
    chatId.value = '';
  } else {
    uni.redirectTo({
      url: '/pages/login/login',
    });
  }
};
</script>

<template>
  <view
    class="chat-container"
    :style="{
      backgroundImage:
        'url(https://huayun-ai-obs-public.huayuntiantu.com/d8eff78e-4c35-4b55-8bf7-e0d0ecfcf653.png?filename=bg_chat.png)',
    }"
  >
    <!-- 添加顶部导航栏 -->
    <header-nav
      :title="appTitle"
      :titleIcon="appAvatarUrl"
      :showRefresh="true"
      :showMore="false"
      @back="handleBack"
      @refresh="handleRefresh"
    >
      <template #right>
        <view class="custom-icon" @click="newChat">
          <LkSvg width="24px" height="24px" src="/static/chat/comment.svg" />
        </view>
        <view class="custom-icon" @click="handleShare">
          <LkSvg width="24px" height="24px" src="/static/chat/share.svg" />
        </view>
      </template>
    </header-nav>

    <view class="chat-messages">
      <scroll-view class="message-list-scroll" scroll-y :scroll-into-view="scrollIntoView">
        <MessageList
          :finalAppId="finalAppId"
          :list="messages"
          :isResponseLoading="isResponseLoading"
          :isParsingBackground="isParsingBackground"
          :isParsingFiles="isParsingFiles"
          :welcomeText="welcomeText"
          :appAvatarUrl="appAvatarUrl"
          @updateMessage="onUpdateMessage"
          @retryMessage="onRetryMessage"
          @updateContent="updateContent"
          @deleteMessage="onDeleteMessage"
        />

        <view id="scroll-bottom1" class="scroll-bottom"></view>
        <view id="scroll-bottom2" class="scroll-bottom"></view>
      </scroll-view>

      <view class="input-container">
        <view v-if="chatting" class="stop-chat" @touchstart="stopChat()"> 停止生成 </view>
        <MessageInput
          ref="messageInputRef"
          :sideBarMessage="sideBarMessage"
          :chatting="chatting"
          :uploadLimit="uploadLimit"
          @scrollToBottom="scrollToBottom(100)"
          @send="startChat"
        />
      </view>

      <ChatFlowStep v-if="selectedFlowStep" :flowStep="selectedFlowStep" @sendBack="sideBarData" />
      <view class="chat-footer">
        <view class="ai-notice-text">内容由AI生成，请核查重要信息</view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-size: contain;
  background-position: bottom;
  background-repeat: no-repeat;
  background-color: #fff;
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.message-list-scroll {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  position: relative;
  height: 700rpx;
}

.scroll-bottom {
  height: 0;
}

.input-container {
  position: relative;
  width: 100%;
}

.stop-chat {
  display: inline-flex;
  padding: 5px 12px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 16rpx;
  background: rgba(0, 0, 0, 0.4);
  font-size: 28rpx;
  color: #fff;
  position: absolute;
  bottom: 100%;
  margin-bottom: 10rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

.quick-bar {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  margin-left: 32rpx;
}

.quick-step {
  display: inline;
  background-color: #ffffff;
  color: #3c6dff;
  border: 1rpx solid #3c6dff;
  padding: 12rpx 48rpx;
  font-size: 28rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
}

.quick-title {
  display: flex;
  align-items: center;
  color: #a9acb1;
  font-size: 22rpx;
  padding-left: 8rpx;
}

.other-tenant {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18rpx;
  margin-left: 32rpx;
  background-color: #f9f9f9;
}

.tenant-left {
  display: flex;
  align-items: center;
}

.tenant-label {
  color: #909399 !important;
}

.tenant-name {
  display: flex;
  align-items: center;
  margin: 0 12rpx;
}

.tenant-avatar {
  width: 32rpx;
  height: 32rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
}

.tenant-title {
  color: #111824;
  font-size: 1.1rem;
  font-weight: bold;
  margin-left: 12rpx;
}

.tenant-close {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.arrow-leftward::before {
  content: '←';
  font-size: 12rpx;
}

.close-icon::before {
  content: '×';
  font-size: 16rpx;
}

.custom-icon {
  margin-left: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ai-notice-text {
  color: var(--text-icon-text-3, #86909c);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 32rpx;
}
</style>
