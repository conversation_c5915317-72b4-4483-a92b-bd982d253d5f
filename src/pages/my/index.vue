<template>
  <view class="user" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <view class="header">
      <!-- <u-navbar :auto-back="false" bgColor="transparent">
				<view slot="left"></view>
				<view slot="center" class="header-title"></view>
			</u-navbar> -->
      <view class="user-parent">
        <template v-if="isLogin" class="user-parent-login">
          <view class="user-parent-box">
            <image
              src="https://huayun-ai-obs-public.huayuntiantu.com/4d0b829d9c01d213ecf1f304610a4a85.png"
            ></image>
            <view class="user-parent-info">
              <view class="user-parent-info-name u-line-1" @tap.stop="handleGotoLogin">登录</view>
              <view class="user-parent-info-account"></view>
            </view>
          </view>
        </template>
        <template v-else>
          <view class="user-parent-box">
            <image class="user-avatar" shape="circle" :src="userInfo?.avatar"></image>
            <view class="user-parent-info">
              <view class="user-parent-info-name u-line-1">{{ userInfo?.username }}</view>
              <view class="user-parent-info-account">{{ userInfo?.phone }}</view>
            </view>
          </view>
        </template>

        <view class="user-edit" v-if="!isLogin">
          <view class="edit-btn" @click="handleGotoModify"> 编辑 </view>
        </view>
      </view>
    </view>
    <view class="user-link-bottom">
      <view class="user-link-bottom-item" @click="handleGotoDialogueRecord">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/ed4e21700d5552664f9c7c39cfb7d0e9.svg"
        />
        <text>对话记录</text>
        <image
          class="user-link-bottom-item-arrow"
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
        />
      </view>
      <view class="user-link-bottom-item" @click="handleGotoRecycleBin">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/bdd7cdfd11eb6dc142ef468f6f58c61e.svg"
        />
        <text>回收站</text>
        <image
          class="user-link-bottom-item-arrow"
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
        />
      </view>
    </view>

    <view class="user-link-bottom">
      <view class="user-link-bottom-item" @click="handleGotoUserFeedback">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/b998a54acfdf9a1dc7994748d00ffeae.svg"
        />
        <text>用户反馈</text>
        <image
          class="user-link-bottom-item-arrow"
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
        />
      </view>
      <view class="user-link-bottom-item" @click="handleGotoUserAgreement">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/aca4d936150798cb592be5dc03c88c39.svg"
        />
        <text>用户协议</text>
        <image
          class="user-link-bottom-item-arrow"
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
        />
      </view>
      <view class="user-link-bottom-item" @click="handleGotoSetting">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/be94ce0f1fd77f567d401a5a3be1cb1f.svg"
        />
        <text>设置</text>
        <image
          class="user-link-bottom-item-arrow"
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg"
        />
      </view>
    </view>
    <u-safe-bottom></u-safe-bottom>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '@/store/userStore';
import { getUser } from '@/api/my-pkg/modify';
import { onLoad, onShow } from '@dcloudio/uni-app';
const userStore = useUserStore();
console.log(userStore.getUserInfo);

const isLogin = computed(() => userStore.getUserInfo === null);

const { safeAreaInsets } = uni.getSystemInfoSync();
const userInfo = ref({
  avatar: '',
  username: '',
  phone: '',
});

onShow(() => {
  fetchGetUser();
});

// 跳转修改
function handleGotoModify() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/modify/index',
  });
}

// 跳转对话记录
function handleGotoDialogueRecord() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/history/index',
  });
}

// 跳转回收站
function handleGotoRecycleBin() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/recycleBin/index',
  });
}

// 跳转用户反馈
function handleGotoUserFeedback() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/feedback/feedback',
  });
}

// 跳转用户协议
function handleGotoUserAgreement() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/user-agreement/index',
  });
}

// 跳转设置
function handleGotoSetting() {
  uni.navigateTo({
    url: '/pages-subpackages/my-pkg/setting/index',
  });
}

// 登录
function handleGotoLogin() {
  uni.redirectTo({
    url: '/pages-subpackages/auth-pkg/login/index',
  });
}

function fetchGetUser() {
  getUser({ id: userStore.getUserInfo?.userId! }).then(res => {
    userInfo.value = res;
  });
}
</script>

<style lang="scss" scoped>
.user {
  position: relative;
  /* min-height: calc(100vh - var(--window-bottom)); */
  background: linear-gradient(180deg, #f3f7ff 0%, rgba(255, 255, 255, 0) 100%);
  background-repeat: no-repeat;
  background-size: 100% 500rpx;
  background-position: top;
  background-attachment: fixed;
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.user * {
  padding: 0;
  margin: 0;
  line-height: 1em;
}

.header {
  box-sizing: border-box;
  background-size: 750rpx 538rpx;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-color: transparent;
  z-index: 999;
  background: url(https://huayun-ai-obs-public.huayuntiantu.com/5fb32bec6d7b521c5f12c42909d15102.png)
    0px 0px / 100% auto no-repeat #f7f7f7;
}

.header-title {
  text-align: center;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 500;
  font-size: 34rpx;
  color: #000000;
}

.user-parent {
  margin-top: 120rpx;
  padding: 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-parent-login {
  padding: 0;
  font-size: 40rpx;
}

.user-parent image {
  flex-shrink: 0;
  border-radius: 50%;
  margin-left: 10rpx;
  margin-right: 44rpx;
  width: 135rpx;
  height: 135rpx;
  box-shadow:
    0rpx 14rpx 23rpx 0rpx rgba(0, 0, 0, 0.05),
    0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  background: #ffffff;
  border: 4rpx solid #fff;
}

.user-parent-box {
  display: flex;
  align-items: center;
}

.user-parent-info-name {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #000000;
}

.user-parent-info-account {
  margin-top: 12rpx;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #6a6a6d;
}

.user-edit {
  width: 104rpx;
  height: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.21);
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
}

.user-content {
  flex: 1;
  overflow: hidden;
  z-index: 999;
}

.user-link-top {
  padding: 60rpx 30rpx 0 30rpx;
  display: flex;
  justify-content: space-around;
}

.user-link-top-item {
  width: 156rpx;
}

.user-link-top-item view {
  text-align: center;
}

.user-link-top-item view:first-child {
  font-size: 38rpx;
  font-weight: 600;
  color: #2f2f2f;
}

.user-link-top-item view:last-child {
  margin-top: 8rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333;
}

.user-link-middle {
  padding: 60rpx 30rpx 0 30rpx;
  display: flex;
  justify-content: space-between;
}

.user-link-middle-item {
  width: 334rpx;
  height: 144rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-sizing: border-box;
  display: inline-flex;
  justify-content: center;
  flex-direction: column;
}

.user-link-middle-item view:first-child {
  padding-left: 20rpx;
  position: relative;
  font-size: 30rpx;
  font-weight: 500;
  color: #333330;
  /* background: url(../../static/login/login_03.png) no-repeat right 12rpx center/40rpx 40rpx; */
}

.user-link-middle-item view:first-child::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2px;
  height: 15px;
  background-color: #fff;
  border-radius: 0 2rpx 2rpx 0;
}

.user-link-middle-item view:last-child {
  padding-top: 20rpx;
  padding-left: 20rpx;
  font-size: 24rpx;
  color: #aaa;
  font-weight: 400;
}

.user-link-bottom {
  margin: 24rpx 32rpx 32rpx 32rpx;
  background-color: #fff;
  border-radius: 24rpx;
}
.user-link-bottom-item-arrow {
  width: 24 * 2rpx;
  height: 24 * 2rpx;
  margin: 0 !important;
  margin-left: auto !important;
}

.user-link-bottom-item {
  margin: 8rpx 32rpx;
  margin-right: 0;
  padding-right: 32rpx;
  height: 112rpx;
  display: flex;
  align-items: center;
  /* background: url(https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/80b8b3d7cea52fb0cfe14c7dc3b8c36a.svg)
    no-repeat right/40rpx 40rpx; */
  border-top: 1px solid #eee;
}

.user-link-bottom-item:first-child {
  border-top: none;
}

.user-link-bottom-item image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.user-link-bottom-item text {
  font-family:
    PingFang SC,
    PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.9);
}
</style>
