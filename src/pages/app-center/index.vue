<template>
  <!-- <view class="container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }"> -->
  <view class="container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- <up-loading-icon v-if="loading" class="loading-icon"></up-loading-icon> -->
    <view
      class="create-app"
      @tap="handleCreateApp"
      :style="{
        bottom: getCreateAppHeight(),
      }"
    >
      <view class="create-app-text">
        <image
          src="https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/f10573eaeac5b525dd70c43373fdbfb9.svg"
        />
        创建应用
      </view>
    </view>

    <!-- 顶部导航栏 -->
    <view class="app-center-header">
      <!-- <u-navbar title="应用中心" :autoBack="false"></u-navbar> -->
      <view class="text-search">
        <view class="text-search-title">应用中心</view>
        <view class="text-search-input" @tap="handleSearch">
          <u-icon name="search" size="48rpx" color="#000"></u-icon>
        </view>
      </view>
      <!-- 锚点定位 tab -->
      <u-tabs
        :list="[
          {
            id: 'personage',
            name: '我的应用',
          },
          ...scenesList,
        ]"
        lineColor="transparent"
        @change="handleChange"
        keyName="name"
        v-model:current="currentTab"
        class="app-list-tabs"
      >
        <template #content="{ item, keyName, index }">
          <view class="app-list-tabs-item">
            <view class="app-list-tabs-item-box">
              <view
                class="app-list-tabs-item-text"
                :class="currentTab === index ? 'active' : 'inactive'"
              >
                {{ item[keyName] }}
              </view>

              <image
                v-show="currentTab === index"
                class="app-list-tabs-item-icon"
                src="https://huayun-ai-obs-public.huayuntiantu.com/a4180d57745271fa2056e474797826f7.png"
              >
              </image>
            </view>
          </view>
        </template>
        <template #left>
          <view class="app-list-tabs_empty"></view>
        </template>
      </u-tabs>
    </view>

    <view class="app-list-container">
      <LkLoading v-if="loading" :loading="loading" class="loading-icon" />

      <!-- 应用列表 -->
      <scroll-view
        scroll-y
        class="app-list"
        :style="{
          height: getAppListHeight(),
        }"
        :scroll-into-view="targetId"
        :show-scrollbar="false"
      >
        <uni-swipe-action
          style="position: relative"
          v-for="(item, outerIndex) in combinedList"
          :id="'tab-' + item?.sceneId"
        >
          <view class="app-list-container" :key="item?.sceneId">
            <view class="app-list-item-title">
              <view class="title-box">
                <view class="title-box-text">{{ item?.name }}</view>
                <image
                  v-if="checkString(item?.name)"
                  class="app-list-item-title-icon"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/77c2698413255275ad29152c8d515e1f.png"
                />
                <image
                  v-else
                  class="app-list-item-title-icon-long"
                  src="https://huayun-ai-obs-public.huayuntiantu.com/33fc7ff8-3dc3-48fc-a832-dc844acc701b.png"
                />
              </view>
            </view>

            <uni-swipe-action-item
              v-for="(appItem, innerIndex) in item?.apps"
              class="app-list-item"
              :auto-close="false"
              :show="openedStates[appItem.id]"
              :disabled="appItem.source !== DataSource.Personal"
              @change="(e: any) => handleSwipeActionChange(e, appItem.id)"
            >
              <view class="app-list-item-content" @tap.stop="handleItemClick(appItem)">
                <view class="icon">
                  <image
                    :src="
                      appItem?.avatarUrl ||
                      'https://huayun-ai-obs-public.huayuntiantu.com/7dd8c2f8d3b0d94cc769ccee58f8b753.svg'
                    "
                  />
                </view>

                <view class="box">
                  <view class="box-title">{{ appItem?.name }}</view>
                  <view class="box-text">{{ appItem.intro }}</view>
                  <view class="box-icon">
                    <image :src="appSourceImgObj[appItem?.source]" />
                    <text>{{ appSourceTextObj[appItem?.source] }}</text>
                  </view>
                </view>

                <view class="btn">
                  <!-- 添加按钮 -->
                  <view
                    class="btn-add"
                    v-if="!appItem.isCommonApp"
                    @tap.stop="!processingIds[appItem.id] && handleSetCommonApp(appItem.id)"
                    :class="{
                      'btn-disabled': processingIds[appItem.id],
                      'guide-target': outerIndex === 1 && innerIndex === 1 && !appItem.isCommonApp,
                    }"
                  >
                    <!-- 如果正在处理该ID，显示loading图标 -->
                    <u-loading-icon
                      v-if="processingIds[appItem.id]"
                      mode="circle"
                      size="18rpx"
                      color="#FFFFFF"
                    ></u-loading-icon>
                    <image
                      v-else
                      src="https://huayun-ai-obs-public.huayuntiantu.com/8e1e394c432bf47be85797884a049ef3.png"
                    />
                  </view>

                  <!-- 完成按钮 -->
                  <view
                    class="btn-done"
                    v-else
                    @tap.stop="!processingIds[appItem.id] && handleRemoveCommonApp(appItem.id)"
                    :class="{ 'btn-disabled': processingIds[appItem.id] }"
                  >
                    <!-- 如果正在处理该ID，显示loading图标 -->
                    <u-loading-icon
                      v-if="processingIds[appItem.id]"
                      mode="circle"
                      size="18rpx"
                      color="#909399"
                    ></u-loading-icon>
                    <image
                      v-else
                      src="https://huayun-ai-obs-public.huayuntiantu.com/ea210556b3771b24d565b52647bc5b61.png"
                    />
                  </view>
                </view>
              </view>

              <template v-slot:right>
                <view class="slide-btn-container">
                  <view v-if="!deleteStates[appItem.id]" class="slide-btn">
                    <view class="slide-btn-item" @touchstart.stop.prevent="handleEditApp(appItem)">
                      <image
                        src="https://huayun-ai-obs-public.huayuntiantu.com/fa316283ed76c7be9d50f6e6f1415037.png"
                      />
                    </view>
                    <view class="slide-btn-item" @touchstart.stop="handleDelete(appItem.id)">
                      <image
                        src="https://huayun-ai-obs-public.huayuntiantu.com/14f831d61dd20af0129258d423ea41c5.png"
                      />
                    </view>
                  </view>
                  <view class="slide-btn" v-else>
                    <view
                      class="slide-pop-btn"
                      @touchstart.stop="confirmDelete(appItem.id, appItem.tmbId)"
                    >
                      <image
                        src="https://huayun-ai-obs-public.huayuntiantu.com/14f831d61dd20af0129258d423ea41c5.png"
                      />
                      <text>确认删除</text>
                    </view>
                  </view>
                </view>
              </template>
            </uni-swipe-action-item>
          </view>
        </uni-swipe-action>
      </scroll-view>
    </view>
  </view>
  <LkToast ref="uToastRef" />
  <LkStepGuide
    :steps="guideSteps"
    :theme="guideTheme"
    :skipEnabled="true"
    @skip="onGuideSkip"
    @complete="onGuideComplete"
    ref="stepGuide"
  />
</template>

<script setup lang="ts">
import {
  deleteClientApp,
  getMyAppList,
  getOtherAppList,
  getTenantSceneList,
  getTenantSceneListOfficialHome,
  rmCommonApp,
  setCommonApp,
} from '@/api/app-center';
import { SearchType } from '@/constants/search';
import { ref, onMounted, computed, watch, toRef, nextTick } from 'vue';
import { useAppStore } from '@/store/useAppStore';
import type { AppListItemType } from '@/types/api/app-center';
import { onLoad, onShow } from '@dcloudio/uni-app';
enum DataSource {
  /** 专属 */
  Tenant = 1,
  /** 官方 */
  Offical = 2,
  /** 个人 */
  Personal = 3,
}
const { safeAreaInsets } = uni.getSystemInfoSync();

const appSourceImgObj = {
  [DataSource.Tenant]:
    'https://huayun-ai-obs-public.huayuntiantu.com/0c161930a6b4315e16d593e7ec1cdbb3.svg',
  [DataSource.Offical]:
    'https://huayun-ai-obs-public.huayuntiantu.com/56bcad1ca41befc676c13c77066acd9d.svg',
  [DataSource.Personal]:
    'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com:443/3dd9642ef52df696fec7f3ccb3fc3cf1.svg',
};

const appSourceTextObj = {
  [DataSource.Tenant]: '专属',
  [DataSource.Offical]: '官方',
  [DataSource.Personal]: '个人',
};

// 元素高度
const customTabbarHeight = ref(0);
const createAppHeight = ref(0);
const appCenterHeaderHeight = ref(0);
const systemInfo = ref<UniNamespace.GetSystemInfoResult>();
/** 顶部应用列表 */
const scenesList = ref<any[]>([]);
const otherApps = ref<any[]>([]);
const currentTab = ref(0);
const uToastRef = ref<any>(null);
// 跳转元素id
const targetId = ref('');
// 控制滑动 - 修改为对象形式
const openedStates = ref<Record<string, 'left' | 'right' | 'none'>>({});
// 控制二次确认删除 - 修改为对象形式
const deleteStates = ref<Record<string, boolean>>({});
/** 应用列表 loading */
const loading = ref(false);

const appStore = useAppStore();
const { loadMyApps } = appStore;

const combinedList = ref<{ name: any; sceneId: any; apps: AppListItemType[] }[]>([]);

// 添加新的状态变量记录正在处理的ID
const processingIds = ref<Record<string, boolean>>({});

// 计算引导目标索引
const guideOuterIdx = ref(-1);
const guideInnerIdx = ref(-1);
watch(
  combinedList,
  val => {
    let found = false;
    for (let i = 0; i < val.length; i++) {
      const apps = val[i].apps || [];
      for (let j = 0; j < apps.length; j++) {
        if (!apps[j].isCommonApp) {
          guideOuterIdx.value = i;
          guideInnerIdx.value = j;
          found = true;
          break;
        }
      }
      if (found) break;
    }
    if (!found) {
      guideOuterIdx.value = -1;
      guideInnerIdx.value = -1;
    }
  },
  { immediate: true }
);
const guideSteps = [
  {
    target: '.app-list-tabs_empty',
    title: '页面标题',
    content: '左右滑分类，应用一触即达。',
    position: 'bottom',
  },
  {
    target: '.create-app',
    title: '菜单项 1',
    content: '创建专属智能应用，懂你所需，高效助力。',
    position: 'top',
  },
  {
    target: '.guide-target',
    title: '',
    content: '选择高频使用应用，添加至「我的常用」中，方便快速找到并使用。',
    position: 'bottom',
    lollipop: true,
    guideStepsStyle: {
      minWidth: '420rpx',
    },
  },
];

// 主题设置：可选 'light' 或 'dark'
const guideTheme = ref('light');

// 引用组件实例
const stepGuide = ref();

// 开始引导
const startGuide = () => {
  stepGuide.value.start();
};

// 处理引导跳过事件
const onGuideSkip = () => {
  console.log('用户跳过了引导');
};

// 处理引导完成事件
const onGuideComplete = () => {
  console.log('用户完成了所有引导步骤');
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, AppCenter: true });
};

function fetchloadMyapps() {
  // 如果已有操作正在进行中，则不显示全局loading
  const hasProcessingItem = Object.values(processingIds.value).some(status => status);

  // 只有当没有单独操作正在处理时，才显示全局loading
  if (!hasProcessingItem) {
    loading.value = true;
  }

  console.log('loading.value', loading.value);
  return loadMyApps()
    .then(myApps => {
      const data = computed(() => {
        return [
          {
            name: '我的应用',
            sceneId: 'personage',
            apps: myApps.filter(({ source }) => source === DataSource.Personal),
          },
          ...scenesList.value.map((scene: any) => {
            const apps = myApps.filter((item: any) =>
              item.labelList?.some((it: any) => String(it.tenantSceneId) === String(scene.id))
            );
            if (scene.name === 'AI评价' && otherApps.value.length > 0) {
              apps.push(...otherApps.value);
            }
            return {
              name: scene.name,
              sceneId: scene.id,
              apps,
            };
          }),
        ];
      });
      combinedList.value = data.value;
      console.log('combinedList', combinedList.value);
      return combinedList.value; // 返回结果，确保Promise链正确传递
    })
    .finally(() => {
      loading.value = false;
    });
}

onShow(async () => {
  // 等待前两个请求完成后再调用fetchloadMyapps
  try {
    await Promise.all([fetchGetTenantSceneList(), fetchGetOtherAppList()]);

    // 前两个请求完成后再加载应用列表
    await fetchloadMyapps();

    nextTick(() => {
      if (!uni.getStorageSync('Guide')?.AppCenter) {
        startGuide();
      }
    });
  } catch (error) {
    console.error('加载数据失败:', error);
    uToastRef.value?.show({
      message: '加载数据失败，请重试',
      type: 'error',
    });
  }
});

onMounted(async () => {
  // 确保DOM已完全渲染
  await nextTick();

  console.log('safeAreaInsets', safeAreaInsets);

  const query = uni.createSelectorQuery();
  query
    .select('.app-center-header')
    .boundingClientRect((data: any) => {
      if (data) {
        appCenterHeaderHeight.value = data.height;
        console.log('appCenterHeaderHeight', appCenterHeaderHeight.value);
      } else {
        console.error('未能获取到.app-center-header元素');
        // 设置一个默认值
        appCenterHeaderHeight.value = 120;
      }
    })
    .exec();

  query
    .select('.custom-tabbar-wrap')
    .boundingClientRect((data: any) => {
      if (data) {
        customTabbarHeight.value = data.height;
        console.log('customTabbarHeight', customTabbarHeight.value);
      } else {
        console.error('未能获取到.custom-tabbar-wrap元素');
        // 设置一个默认值
        customTabbarHeight.value = 50;
      }
    })
    .exec();

  query
    .select('.create-app')
    .boundingClientRect((data: any) => {
      createAppHeight.value = data.height;
    })
    .exec();

  uni.getSystemInfo({
    success: res => {
      systemInfo.value = res;
    },
  });
});

/** 获取应用列表 */
function fetchGetTenantSceneList() {
  return getTenantSceneList().then(res => {
    scenesList.value = res;
    return res; // 返回结果，确保Promise链正确传递
  });
}

function fetchGetMyAppList() {
  console.log('wdnmd-fetchGetMyAppList');
  return getMyAppList({}).then(res => {
    console.log('wdnmd-fetchGetMyAppList', res);
    return res;
  });
}

function fetchGetTenantSceneListOfficialHome() {
  return getTenantSceneListOfficialHome({}).then(res => {
    console.log('wdnmd-fetchGetTenantSceneListOfficialHome', res);
    return res;
  });
}

function fetchGetOtherAppList() {
  return getOtherAppList().then(res => {
    console.log('获取其他应用', res);
    otherApps.value = res;
    return res; // 返回结果，确保Promise链正确传递
  });
}

function handleChange(item: any) {
  console.log(item);
  scrollToAnchor(item.id);
}

// 点击项目内容区域
function handleItemClick(appItem: any) {
  // 这里可以添加进入应用详情页或其他逻辑
  console.log('点击应用项', appItem);
  // 如果有滑出状态，关闭滑出
  if (openedStates.value[appItem.id] === 'right') {
    openedStates.value[appItem.id] = 'none';
    deleteStates.value[appItem.id] = false;
  }
}

// 点击删除按钮
function handleDelete(id: string) {
  deleteStates.value[id] = true;
}

// 确认删除
function confirmDelete(id: string, tmbId: string) {
  // 这里添加实际删除应用的逻辑
  console.log('确认删除应用id', id, tmbId);

  deleteClientApp({ id, tmbId })
    .then(res => {
      console.log('删除应用', res);
      // 显示删除成功提示
      uToastRef.value.show({
        message: '删除成功',
        type: 'success',
      });
    })
    .finally(() => {
      // 删除后关闭所有滑动
      Object.keys(openedStates.value).forEach(key => {
        openedStates.value[key] = 'none';
      });
      // 删除后关闭所有滑动
      Object.keys(deleteStates.value).forEach(key => {
        deleteStates.value[key] = false;
      });

      // 删除后关闭对应的滑动
      // openedStates.value[id] = 'none';
      // deleteStates.value[id] = false;

      // 重新加载应用列表
      fetchloadMyapps();
    });
}

// 滑动状态改变
function handleSwipeActionChange(status: 'left' | 'right' | 'none', id: string) {
  console.log('滑动状态改变', status, id);

  // 记录当前滑动状态
  openedStates.value[id] = status;

  // 如果关闭了滑动，重置删除确认状态
  if (status === 'none') {
    deleteStates.value[id] = false;
  }
}

function handleCreateApp() {
  uni.navigateTo({
    url: '/pages-subpackages/app-center-pkg/create-app/index',
  });
}

// 创建应用高度
function getCreateAppHeight() {
  if (systemInfo.value?.uniPlatform === 'web') {
    return '9%';
  } else {
    switch (systemInfo.value?.platform) {
      case 'ios':
        return '12%';
      case 'android':
        return '140rpx';
    }
  }
}

function handleSearch() {
  uni.navigateTo({
    url: '/pages/search/index?activeType=' + SearchType.APP,
  });
}

async function scrollToAnchor(anchorId: string) {
  targetId.value = `tab-${anchorId}`;
}

// 设为常用app
function handleSetCommonApp(id: string) {
  // 记录当前正在处理的ID
  processingIds.value[id] = true;

  setCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已添加至首页-我的常用',
        type: 'success',
      });
    })
    .finally(() => {
      // 先将当前ID的处理状态重置
      processingIds.value[id] = false;
      // 然后重新加载应用列表
      fetchloadMyapps();
    });
}

// 移除常用app
function handleRemoveCommonApp(id: string) {
  // 记录当前正在处理的ID
  processingIds.value[id] = true;

  rmCommonApp({ id })
    .then(res => {
      uToastRef.value.show({
        message: '已从首页-我的常用中移除',
        type: 'success',
      });
    })
    .finally(() => {
      // 先将当前ID的处理状态重置
      processingIds.value[id] = false;
      // 然后重新加载应用列表
      fetchloadMyapps();
    });
}

// 点击编辑应用
function handleEditApp(params: any) {
  console.log('点击编辑应用', params);
  // 先关闭滑动状态
  if (params.id) {
    openedStates.value[params.id] = 'none';
  }

  // 防止事件重复触发，使用setTimeout延迟导航
  uni.navigateTo({
    url: `/pages-subpackages/app-center-pkg/edit-app/index?id=${params.id}`,
  });
}

function getAppListHeight() {
  // 获取底部安全区域高度
  const safeAreaBottom = safeAreaInsets?.bottom || 0;

  // 计算实际可用高度
  if (systemInfo.value?.platform === 'ios') {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px + ${safeAreaBottom}px))`;
  } else {
    return `calc(100vh - (${appCenterHeaderHeight.value}px + ${customTabbarHeight.value}px))`;
  }
}

function checkString(str: string) {
  const isAllChinese = /^[\u4e00-\u9fa5]+$/.test(str);
  const hasAlpha = /[a-zA-Z]/.test(str);

  if (str.length < 4) {
    return true;
  } else if (hasAlpha && str.length <= 4) {
    return true;
  } else if (isAllChinese && str.length <= 4) {
    return false;
  } else {
    return false;
  }
}
console.log('checkString', checkString('评价'));
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: url('https://huayun-ai-obs-public.huayuntiantu.com/83fb33156350a87984c65ad3d7493509.png')
    no-repeat center center;
  background-size: 100% 100%;
  overflow: hidden; /* 确保容器不可滚动 */
}

.create-app {
  position: fixed;
  bottom: 70 * 2rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  width: 135 * 2rpx;
  height: 40 * 2rpx;
  padding: 8 * 2rpx 16 * 2rpx;
  justify-content: center;
  align-items: center;
  gap: 4 * 2rpx;
  flex-shrink: 0;
  border-radius: 100 * 2rpx;
  background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
  box-shadow: 0px 4 * 2rpx 4 * 2rpx 0px rgba(154, 142, 255, 0.23);
  color: #fff;
  image {
    width: 20 * 2rpx;
    height: 20 * 2rpx;
  }
  .create-app-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14 * 2rpx;
    image {
      width: 20 * 2rpx;
      height: 20 * 2rpx;
    }
  }
}

.app-center-header {
  flex-shrink: 0; /* 防止头部被压缩 */
  z-index: 10; /* 确保头部在滚动内容之上 */
}

.text-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  padding-top: 30 * 2rpx;
  .text-search-title {
    font-size: 20 * 2rpx;
    font-weight: 600;
  }
}

/* tab 高度设置 */
.app-list-tabs {
  position: relative;
  &_empty {
    position: absolute;
    top: -2rpx;
    left: 0;
    right: 0;
    bottom: -2rpx;
    border-radius: 8px;
  }

  .app-list-tabs-item {
    display: flex;
    align-items: center;
    height: 48 * 2rpx;
  }

  // 选中状态
  .active {
    font-size: 36rpx;
    color: #1d2129;
  }

  // 未选中状态
  .inactive {
    font-size: 32rpx;
    color: #4e5969;
  }

  .app-list-tabs-item-box {
    position: relative;
    overflow: visible;
    height: 24 * 2rpx;
    text-align: center;
    line-height: 24 * 2rpx;
  }

  .app-list-tabs-item-text {
    display: inline-block;
    position: relative;
    white-space: nowrap;
    overflow: auto;
    height: 100%;
    z-index: 2;
  }

  .app-list-tabs-item-icon {
    position: absolute;
    top: -16%;
    right: -20%;
    width: 23 * 2rpx;
    height: 20 * 2rpx;
    z-index: 1;
  }
}
.app-list-container {
  position: relative;
  margin: 0 16 * 2rpx;
  border-radius: 14 * 2rpx;
  overflow: hidden;
}

.app-list {
  flex: 1; /* 占据剩余所有空间 */
  overflow-y: scroll; /* 只允许垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 在iOS上提供更好的滚动体验 */
  /* padding: 16 * 2rpx; */
  /* padding-bottom: calc(48 * 2rpx + 50px); */
  position: relative;
  padding-bottom: 28 * 2rpx;
  box-sizing: border-box;

  .app-list-container {
    /* width: 343 * 2rpx; */
    width: 100%;
    background-color: #fff;
    border-radius: 14 * 2rpx;
    margin: 0 auto;
    margin-bottom: 14 * 2rpx;
  }

  .app-list-item-title {
    position: relative;
    color: #000;
    font-size: 18 * 2rpx;
    font-weight: 500;
    height: 51 * 2rpx;
    border-bottom: 1px solid #f2f3f5;
    padding-top: 12 * 2rpx;
    padding-left: 16 * 2rpx;

    .title-box {
      position: relative;
      display: inline-block;
      .title-box-text {
        position: relative;
        z-index: 2;
      }
      .app-list-item-title-icon {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 53 * 2rpx;
        width: 100%;
        height: 9 * 2rpx;
        z-index: 1;
      }
      .app-list-item-title-icon-long {
        position: absolute;
        left: 0;
        bottom: -4 * 2rpx;
        width: 63 * 2rpx;
        width: 100%;
        height: 16 * 2rpx;
        z-index: 1;
      }
    }
  }

  .app-list-item {
    width: 343 * 2rpx;
    height: 88 * 2rpx;
    margin: 0 auto;

    .app-list-item-content {
      display: flex;
      width: 343 * 2rpx;
      height: 88 * 2rpx;
      /* background-color: skyblue; */
      align-items: center;
      padding: {
        left: 16 * 2rpx;
        right: 14 * 2rpx;
        top: 12 * 2rpx;
        bottom: 12 * 2rpx;
      }
      .icon {
        width: 58 * 2rpx;
        height: 58 * 2rpx;
        background-color: #d8e1ff;
        border-radius: 50 * 2rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }

      .box {
        width: 203 * 2rpx;
        margin: 0 10 * 2rpx;
        .box-title {
          font-size: 16 * 2rpx;
          font-weight: 500;
          color: #000;
          line-height: 24 * 2rpx;
        }
        .box-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #606266;
          font-size: 12 * 2rpx;
          font-weight: 400;
          line-height: 20 * 2rpx;
        }
        .box-icon {
          display: flex;
          align-items: center;
          font-size: 12 * 2rpx;
          color: #909399;
          font-weight: 400;
          line-height: 16 * 2rpx;
          image {
            width: 14 * 2rpx;
            height: 14 * 2rpx;
            margin-right: 4 * 2rpx;
          }
        }
      }

      .btn {
        .btn-add {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background: linear-gradient(180deg, #ab57ff 0%, #7d4dff 100%);
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
        .btn-done {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28 * 2rpx;
          height: 28 * 2rpx;
          background-color: #f2f3f5;
          border-radius: 100 * 2rpx;
          image {
            width: 18 * 2rpx;
            height: 18 * 2rpx;
          }

          &.btn-disabled {
            opacity: 0.7;
            pointer-events: none;
          }
        }
      }
    }
  }

  .slide-btn-container {
    width: 52 * 2 * 2rpx;

    .slide-btn {
      display: flex;
      height: 100%;

      .slide-pop-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;
        background-color: #db443c;
        text {
          color: #fff;
          font-size: 14 * 2rpx;
        }
        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
      }

      .slide-btn-item {
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        height: 100%;

        image {
          width: 20 * 2rpx;
          height: 20 * 2rpx;
        }
        &:nth-child(1) {
          background-color: #7d4dff;
        }
        &:nth-child(2) {
          background-color: #db443c;
        }
      }
    }
  }
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10001;
}
</style>
