<template>
  <SplashAd
    v-if="systemStore.getShowSplashAd"
    :imageUrl="adImageUrl"
    :duration="5"
    adLink=""
    @close="handleAdClose"
  />
  <LkWholePagination ref="wholePaginationRef" :fetch-method="currentFetchMethod" :page-size="10">
    <template #default="{ list = [] }">
      <view class="container" @click="handleContainerClick">
        <!-- 顶部导航栏 -->
        <view class="navbar">
          <view class="navbar-content">
            <LkTenantSwitch @switch="handleTenantSwitch" />
            <up-icon name="search" size="48rpx" color="black" @click="handleSearch" />
          </view>
        </view>
        <!-- 精选推荐 -->
        <view class="home_recommend">
          <view class="home_title" style="padding-left: 20rpx; padding-right: 20rpx">
            <view style="position: relative; z-index: 1">精选推荐</view>
            <LkServiceImage
              name="textLine"
              style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
            />
          </view>
          <template v-if="recommendListFilter.length">
            <view
              class="home_recommend_body"
              :style="{
                transition: 'max-height 0.3s ease-in-out, opacity 0.3s ease',
                maxHeight: recommendMaxHeight,
                overflow: 'hidden',
              }"
            >
              <up-grid :border="false" :col="gapLen" gap="20rpx">
                <up-grid-item v-for="(item, index) in recommendListFilter" :key="item.id">
                  <up-transition
                    :show="recommendListFilter.length > 0"
                    mode="fade-zoom"
                    :class="`home_recommend_body-item gap-${gapLen} bg-gradient-${index % 8}`"
                  >
                    <LkText
                      type="primary"
                      bold
                      :class="`${gapLen > 2 ? 'up-line-1' : 'up-line-2'}`"
                      >{{ item.appName || '--' }}</LkText
                    >
                    <LkText
                      type="tertiary"
                      :class="`${gapLen > 2 ? 'up-line-1' : 'up-line-2'}`"
                      style="margin-top: 10rpx; font-size: 20rpx"
                      >{{ item.appIntro || '--' }}</LkText
                    >
                  </up-transition>
                </up-grid-item>
              </up-grid>
            </view>
            <view class="home_recommend-more" @click="handleToggleRecommend">
              <view class="home_recommend-more-body" v-if="showRecommendMore">
                <image
                  src="https://huayun-ai-obs-public.huayuntiantu.com/7d8eacd8-2fe3-4684-99d5-79c4b5c9e005.png"
                  class="home_recommend-more-body-bg"
                />
                <uni-icons
                  type="bottom"
                  size="14"
                  color="#000"
                  class="home_recommend-more-body-icon"
                  :style="{
                    transform: `translateX(-40%) rotate(${isRecommendCollapse ? -180 : 0}deg)`,
                    transition: 'transform 0.3s ease',
                  }"
                />
              </view>
            </view>
          </template>
          <view class="home_empty" v-else>
            <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
            <LkText type="tertiary" size="small">暂无数据</LkText>
          </view>
          <view
            class="home_recommend-guide"
            :style="`bottom: ${gapLen === 3 ? '30' : gapLen === 2 ? '20' : '20'}rpx`"
          />
        </view>
        <!-- 我的常用 -->
        <view class="home_common-use">
          <view class="home_common-use_header">
            <view class="home_title">
              <view style="position: relative; z-index: 1">我的常用</view>
              <LkServiceImage
                name="textLine"
                style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
              />
            </view>
            <view class="home_tools" v-if="commonUseListFilter.length">
              <template v-if="!isCommonUseEdit">
                <up-icon name="grid" size="44rpx" @tap="onAddApp" />
                <up-icon name="edit-pen" size="44rpx" @tap="() => (isCommonUseEdit = true)" />
              </template>
              <template v-else>
                <LkButton
                  type="secondary"
                  shape="round"
                  size="small"
                  style="font-size: 20rpx; height: 50rpx; padding: 0 30rpx"
                  @tap="handeCancelCommonUse(true)"
                  >取消</LkButton
                >
                <LkButton
                  type="primary"
                  shape="round"
                  size="small"
                  style="font-size: 20rpx; height: 50rpx; padding: 0 30rpx"
                  @tap="handeEditCommonUse"
                  >完成</LkButton
                >
              </template>
            </view>
          </view>
          <template v-if="commonUseListFilter.length">
            <view
              class="home_common-use_body"
              :style="{
                paddingTop: '20rpx',
                maxHeight: commonUseSectionMaxHeight,
                transition: 'max-height 0.3s ease-in-out',
                overflow: 'hidden',
              }"
            >
              <LDrag
                ref="dragRef"
                longpress
                :list="commonUseListFilter"
                gridHeight="330rpx"
                :disabled="!isCommonUseEdit"
                @change="handleDragChange"
                @cancel-edit="handeCancelCommonUse(true)"
              >
                <template #grid="{ content: item, active, index, oldindex, oindex }">
                  <view
                    :key="item.id"
                    class="home_common-use_body-grid drag-safe-area"
                    :data-index="index % 2 === 0 ? 'even' : 'odd'"
                    :class="{
                      'item-hidden': index >= 4 && !isCommonUseCollapse,
                      'item-visible': !(index >= 4 && !isCommonUseCollapse),
                    }"
                    :style="{
                      pointerEvents: index >= 4 && !isCommonUseCollapse ? 'none' : 'auto',
                    }"
                  >
                    <view
                      class="home_common-use_body-item"
                      :class="{
                        'item-content-hidden': index >= 4 && !isCommonUseCollapse,
                        'item-content-visible': !(index >= 4 && !isCommonUseCollapse),
                      }"
                      :style="{
                        transformOrigin: 'center',
                      }"
                    >
                      <up-image
                        :src="item.tenantApp.avatarUrl"
                        shape="circle"
                        width="100rpx"
                        height="100rpx"
                      />
                      <LkText type="secondary" style="margin-top: 10rpx" class="up-line-1">{{
                        item.tenantApp.name || '--'
                      }}</LkText>
                      <LkText
                        type="tertiary"
                        class="up-line-1"
                        style="margin-block: 10rpx; font-size: 20rpx"
                        >{{ item.tenantApp.intro || '--' }}</LkText
                      >
                      <view class="home_common-use_body-item-bt" @tap="handleClick(item)">
                        <span class="home_common-use_body-item-bt-font">使用</span>
                      </view>
                      <view
                        class="home_common-use_body-item-del"
                        v-if="isCommonUseEdit"
                        @tap="onRmCommonApp(oindex, item.id)"
                      >
                        <up-icon name="minus-circle-fill" color="#F12409" size="48rpx" />
                      </view>
                    </view>
                  </view>
                </template>
              </LDrag>
            </view>
            <view
              class="home_common-use_collapse l-drag-exclude drag-safe-area"
              @tap="handeCollapseCommonUse"
            >
              <view
                class="home_common-use_collapse-content l-drag-exclude"
                v-if="commonUseList.length > 4"
              >
                <LkText type="tertiary" size="small" class="l-drag-exclude">{{
                  !isCommonUseCollapse ? '展开全部' : '收起全部'
                }}</LkText>
                <up-icon
                  name="arrow-down"
                  :class="{ 'icon-rotated': isCommonUseCollapse, 'l-drag-exclude': true }"
                />
              </view>
            </view>
          </template>
          <view class="home_common-use_empty" v-else @tap="onAddApp">
            <view class="icon">
              <up-icon name="plus" color="#7D4DFF" size="28rpx" />
            </view>
            <span>添加高频使用应用</span>
          </view>
        </view>
        <!-- 最近使用 -->
        <view class="home_recent">
          <view class="home_title">
            <view style="position: relative; z-index: 1">最近使用</view>
            <LkServiceImage
              name="textLine"
              style="position: absolute; bottom: -10rpx; height: 32rpx; width: 125rpx; z-index: 0"
            />
          </view>
          <view class="home_recent-tab">
            <LkTabGroup
              v-model="currentType"
              :tabs="tabOptions"
              @update:modelValue="handleTabChange"
            />
          </view>
          <template v-if="currentType === SearchType.FILE">
            <FileItemList
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              @ok="handleOk"
            />
          </template>
          <template v-else-if="currentType === SearchType.APP">
            <AppItemList
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              :uToastRef="uToastRef"
              style="margin-top: 20rpx"
              @ok="handleOk"
            />
          </template>
          <template v-else-if="currentType === SearchType.CHAT">
            <ChatItemList
              :list="Array.isArray(list) ? list : (list as any)?.records || []"
              style="margin-top: 20rpx"
              @ok="handleOk"
            />
          </template>
        </view>
      </view>
    </template>
    <template #empty>
      <view class="home_empty" style="padding-block: 40rpx; background: #fff">
        <LkServiceImage name="empty" style="width: 200rpx; height: 200rpx" />
        <LkText type="tertiary" size="small">暂无使用数据</LkText>
      </view>
    </template>
  </LkWholePagination>
  <LkToast ref="uToastRef"></LkToast>
  <LkStepGuide
    :steps="guideSteps"
    :theme="guideTheme"
    :skipEnabled="true"
    @skip="onGuideSkip"
    @complete="onGuideComplete"
    ref="stepGuide"
  />
</template>

<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import { onMounted, ref, computed, reactive, nextTick, watch } from 'vue';
import { useSystemStore } from '@/store/systemStore';
import { useUserStore } from '@/store/userStore';
import { useTabStore } from '@/store/tabStore';
import LkTenantSwitch from '@/components/LkTenantSwitch/index.vue';
import LkServiceImage from '@/components/LkServiceImage/index.vue';
import LDrag from '@/uni_modules/lime-drag/components/l-drag/l-drag.vue';
import { SearchType, SearchTypeLabel } from '@/constants/search';
import {
  getRecommendList,
  commonAppList,
  setCommonApp,
  rmCommonApp,
  sortCommonApp,
  recentlyFileList,
  recentlyAppCenterList,
  recentlyChatList,
} from '@/api/userCommon';
import type { CommonAppType, RecommendModuleItem } from '@/types/api/app';
import LkWholePagination from '@/pages/home/<USER>';
import FileItemList from '@/pages/home/<USER>';
import ChatItemList from '@/pages/home/<USER>';
import AppItemList from '@/pages/home/<USER>';

interface PageResponse<T = any> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  realTotal: number;
}

const commonUseList = ref<CommonAppType[]>([]);
const tabStore = useTabStore();
const uToastRef = ref();

const recommendListFilter = ref<RecommendModuleItem[]>([]);

const getCommonAppList = () => {
  commonAppList().then(res => {
    commonUseList.value = res;
  });
};

const onRecommendList = () => {
  getRecommendList().then(res => {
    recommendListFilter.value = res;
    nextTick(() => {
      if (!uni.getStorageSync('Guide')?.Home) {
        startGuide();
      }
    });
  });
};

const onInit = () => {
  getCommonAppList();
  onRecommendList();
};

onShow(() => {
  onInit();
});

const guideSteps = [
  {
    target: '.home_recommend-guide',
    title: '页面标题',
    content: '官方精选推荐应用，一键畅用。',
    position: 'bottom',
  },
  {
    target: '.home_common-use_empty',
    title: '菜单项 1',
    content: '高频使用的应用，可添加至「我的常用」中，一键直达。',
    position: 'top',
  },
];

// 主题设置：可选 'light' 或 'dark'
const guideTheme = ref('light');

// 引用组件实例
const stepGuide = ref();

// 开始引导
const startGuide = () => {
  stepGuide.value.start();
};

// 处理引导跳过事件
const onGuideSkip = () => {
  console.log('用户跳过了引导');
};

// 处理引导完成事件
const onGuideComplete = () => {
  console.log('用户完成了所有引导步骤');
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, Home: true });
};

const adImageUrl = ref('/static/logo.png');

const systemStore = useSystemStore();
const userStore = useUserStore();

const isCommonUseCollapse = ref(false);

const commonUseListFilter = computed(
  () => commonUseList.value
  // !isCommonUseCollapse.value ? commonUseList.value.slice(0, 4) : commonUseList.value
);

const dragRef = ref<any>(null);

const handeCollapseCommonUse = () => {
  isCommonUseCollapse.value = !isCommonUseCollapse.value;
};

// 我的常用
const isCommonUseEdit = ref(false);
const commonUseParams = ref<{ id: string; sort: number }[]>([]);

const handleDragChange = (list: any[]) => {
  const newList = JSON.parse(JSON.stringify(list));
  const param = newList.map((item: any, index: any) => ({
    id: item.content.id,
    sort: index,
  }));
  commonUseParams.value = param;
};
const handeCancelCommonUse = (val?: boolean) => {
  isCommonUseEdit.value = false;
  commonUseParams.value = [];

  // 停止拖拽组件的抖动
  if (dragRef.value && dragRef.value.stopShaking) {
    dragRef.value.stopShaking();
  }

  if (val) {
    const list = JSON.parse(JSON.stringify(commonUseList.value));
    commonUseList.value = [];
    commonUseList.value = list;
  }
};
const handeEditCommonUse = async () => {
  const removedItems = JSON.parse(
    JSON.stringify(
      commonUseList.value.filter(item => !commonUseParams.value.some(cur => cur.id === item.id))
    )
  );
  if (removedItems.length) {
    for (const item of removedItems) {
      await handleDelApp(item.tenantApp.id);
    }
  }
  if (commonUseParams.value.length) {
    await sortCommonApp({ param: commonUseParams.value });
  }
  uToastRef.value.show({
    type: 'success',
    message: '我的常用编辑完成',
    icon: 'checkmark-circle-fill',
  });

  // 停止拖拽组件的抖动
  if (dragRef.value && dragRef.value.stopShaking) {
    dragRef.value.stopShaking();
  }

  setTimeout(() => {
    getCommonAppList();
    handeCancelCommonUse();
  }, 200);
};

const onAddApp = () => {
  // 切换到应用列表
  tabStore.switchTab(1);
};

// 移除常用
const onRmCommonApp = (oindex: number, id: string) => {
  if (dragRef.value && typeof oindex === 'number' && oindex >= 0) {
    // 用cloneList判断是否为最后一个元素
    if (dragRef.value.cloneList?.length === 1) {
      uni.showModal({
        title: '确认移除',
        content: '这是最后一个常用应用，确定要移除吗？',
        success: res => {
          if (res.confirm) {
            dragRef.value.remove(oindex);
            handeEditCommonUse();
          }
        },
      });
    } else {
      dragRef.value.remove(oindex);
    }
  } else {
    console.error('无效的删除操作：dragRef或oindex无效', { dragRef: !!dragRef.value, oindex });
  }
};

const handleDelApp = (id: string) => {
  // 先在本地更新UI
  const appIndex = commonUseList.value.findIndex(app => app.tenantApp.id === id);
  if (appIndex !== -1) {
    // 将要删除的应用暂存
    const removedApp = commonUseList.value[appIndex];
    // 从列表中移除
    commonUseList.value.splice(appIndex, 1);

    rmCommonApp({ id })
      .then(res => {
        uToastRef.value.show({
          message: '已移除',
          type: 'success',
        });
      })
      .catch(() => {
        // 操作失败时恢复列表
        commonUseList.value.splice(appIndex, 0, removedApp);
        uToastRef.value.show({
          message: '移除失败',
          type: 'error',
        });
      });
  }
};

const recentTabChecked = ref('文件');

const handeRecentTabChecked = (val: string) => {
  recentTabChecked.value = val;
};

const isRecommendCollapse = ref(false);

const gapLen = computed(() => {
  const length = recommendListFilter.value.length;
  return length > 4 ? 3 : length >= 2 && length <= 4 ? 2 : 1;
});

const showRecommendMore = computed(() => {
  if (gapLen.value === 3 && recommendListFilter.value.length > 6) {
    return true;
  }
  if (gapLen.value === 2 && recommendListFilter.value.length > 4) {
    return true;
  }
  return false;
});

// 租户切换回调
const handleTenantSwitch = () => {
  // 重新加载页面
  uToastRef.value?.show({
    type: 'success',
    message: '账号切换成功',
  });
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/index/index?switchSuccess=1',
    });
  }, 1000);
};

// 广告关闭回调
const handleAdClose = () => {
  systemStore.setShowSplashAd(false);
  console.log('广告已关闭，显示应用内容');
};

// 确保在组件挂载时清空数据
onMounted(() => {
  if (userStore.userInfo && !userStore.userInfo.menuCodes) {
    // 如果已登录但没有权限信息，模拟设置一些权限
    const userInfo = userStore.userInfo;
    userInfo.menuCodes = ['data']; // 模拟拥有全部权限
    userStore.setUserInfo(userInfo);
  }

  // 重置所有类型的加载状态
  Object.keys(loadedTypes.value).forEach(key => {
    loadedTypes.value[key as SearchType] = false;
  });

  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
});

const handleClick = (item: any) => {
  if (isCommonUseEdit.value) return;
  uni.setStorageSync('selectApp', item.tenantApp);
  uni.navigateTo({
    url: '/pages/chat/index',
  });
};

const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index?activeType=' + SearchType.APP,
  });
};

const handleToggleRecommend = () => {
  isRecommendCollapse.value = !isRecommendCollapse.value;
};

// 优化最大高度计算方法
const recommendMaxHeight = computed(() => {
  const itemsPerRow = gapLen.value; // 从gapLen获取每行显示的卡片数
  const itemHeight = 245; // 每个卡片的基本高度(rpx)
  const baseMargin = 20; // 间距
  const totalItems = recommendListFilter.value.length;

  if (totalItems === 0) return '0px';

  // 如果是展开状态，显示所有卡片
  if (isRecommendCollapse.value) {
    const rows = Math.ceil(totalItems / itemsPerRow);
    return `${rows * itemHeight + (rows - 1) * baseMargin}rpx`;
  } else {
    // 收起状态：默认显示最多2行
    const maxRows = itemsPerRow > 1 ? 2 : 1;
    return `${maxRows * itemHeight + (maxRows - 1) * baseMargin}rpx`;
  }
});

const commonUseSectionMaxHeight = computed(() => {
  const itemsPerRow = 2; // 每行显示2个卡片
  const itemHeight = 330; // 每个卡片的高度(rpx)，包括内容和间距
  const totalItems = commonUseListFilter.value.length;

  if (totalItems === 0) return '0px';

  // 收起状态：最多显示4个卡片（2行）
  // 展开状态：显示所有卡片
  const visibleItems = isCommonUseCollapse.value ? totalItems : Math.min(totalItems, 4);
  const rows = Math.ceil(visibleItems / itemsPerRow);

  return `${rows * itemHeight}rpx`;
});

// 最近使用
const currentType = ref<SearchType>(SearchType.FILE);
const wholePaginationRef = ref<any>(null);

const currentFetchMethod = computed(() => {
  if (currentType.value === SearchType.FILE)
    return async (params: any): Promise<PageResponse<any>> => {
      const res = await recentlyFileList();
      if (
        res &&
        typeof res === 'object' &&
        'records' in res &&
        Array.isArray((res as any).records)
      ) {
        return res as unknown as PageResponse<any>;
      }
      return {
        records: res,
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  if (currentType.value === SearchType.APP)
    return async (params: any): Promise<PageResponse<any>> => {
      const res = await recentlyAppCenterList();
      if (
        res &&
        typeof res === 'object' &&
        'records' in res &&
        Array.isArray((res as any).records)
      ) {
        return res as unknown as PageResponse<any>;
      }
      return {
        records: res,
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  if (currentType.value === SearchType.CHAT) {
    return async (params: any): Promise<PageResponse<any>> => {
      const res = await recentlyChatList(params);
      if (
        res &&
        typeof res === 'object' &&
        'records' in res &&
        Array.isArray((res as any).records)
      ) {
        return res as unknown as PageResponse<any>;
      }
      return {
        records: [],
        total: 0,
        size: params?.size || 10,
        current: params?.current || 1,
        pages: 1,
        realTotal: 0,
      };
    };
  }
  return async (params: any): Promise<PageResponse<any>> => {
    return {
      records: [],
      total: 0,
      size: params?.size || 10,
      current: params?.current || 1,
      pages: 1,
      realTotal: 0,
    };
  };
});

const tabOptions = ref([
  { value: SearchType.FILE, label: SearchTypeLabel[SearchType.FILE] },
  { value: SearchType.APP, label: SearchTypeLabel[SearchType.APP] },
  { value: SearchType.CHAT, label: SearchTypeLabel[SearchType.CHAT] },
]);

// 当点击最近使用的Tab时，确保刷新数据
const handleTabChange = (value: string | number) => {
  if (currentType.value === value) {
    // 如果点击的是当前标签，也刷新数据
    nextTick(() => {
      if (wholePaginationRef.value) {
        wholePaginationRef.value.refresh();
      }
    });
    return;
  }
  currentType.value = value as SearchType;
};

// 当任何列表项操作完成后处理
const handleOk = (type?: string) => {
  if (type === 'add') {
    getCommonAppList();
  }
  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
};

watch(currentType, () => {
  nextTick(() => {
    wholePaginationRef.value?.refresh();
  });
});

const loadedTypes = ref<Record<string, boolean>>({
  [SearchType.FILE]: false,
  [SearchType.APP]: false,
  [SearchType.CHAT]: false,
});

// 处理容器点击，兼容APP/小程序端，H5端不处理
const handleContainerClick = (e: any) => {
  // #ifdef H5
  return;
  // #endif

  // 获取点击点坐标
  const x = e.detail?.x;
  const y = e.detail?.y;
  if (typeof x !== 'number' || typeof y !== 'number') {
    dragRef.value?.stopShaking && dragRef.value.stopShaking();
    return;
  }

  uni
    .createSelectorQuery()
    .selectAll('.drag-safe-area')
    .boundingClientRect(result => {
      const rects = Array.isArray(result) ? result : [result];
      const inSafeArea = rects.some(rect => {
        if (!rect) return false;
        return (
          x >= (rect.left ?? -Infinity) &&
          x <= (rect.right ?? Infinity) &&
          y >= (rect.top ?? -Infinity) &&
          y <= (rect.bottom ?? Infinity)
        );
      });
      if (!inSafeArea) {
        dragRef.value?.stopShaking && dragRef.value.stopShaking();
      }
    })
    .exec();
};
</script>

<style lang="scss" scoped>
@import 'uview-plus/theme.scss';

.container {
  display: flex;
  flex-direction: column;
  background: url('https://huayun-ai-obs-public.huayuntiantu.com/af8e099f-e740-43ef-aa04-52c9dc02eda5.png')
    no-repeat;
  background-size: 100% 60%;
  // background-size: contain;
  position: relative;
  z-index: 1;
  transition: background 0.3s ease;
}

.navbar {
  padding: var(--status-bar-height) 20rpx 0 20rpx;

  .navbar-content {
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.home_bgImg {
  width: 100%;
  position: absolute;
  top: 0;
  z-index: -1;
}

.home_title {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  font-size: 30rpx;
}
.home_tools {
  display: flex;
  align-items: center;
  gap: 15rpx;
  height: 54rpx;
}

.home_empty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-block: 40rpx 0;
}

.home_recommend {
  position: relative;
  z-index: 1;
  padding-bottom: 20rpx;
  margin-top: 20rpx;
  &-guide {
    position: absolute;
    background-color: transparent;
    top: -10rpx;
    left: 8rpx;
    bottom: 30rpx;
    right: 8rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    z-index: -10;
  }
  &_body {
    padding-left: 20rpx;
    padding-right: 20rpx;
    margin-block: 20rpx 15rpx;
    ::v-deep .u-grid-item--hover-class {
      opacity: 1;
    }
    &-item {
      border-radius: 54rpx;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      min-height: 240rpx;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      $color-list: linear-gradient(155deg, #ebf0ff 11.45%, #dfe5fd 100%)
        linear-gradient(155deg, #fff0eb 11.45%, #ffe6de 100%)
        linear-gradient(155deg, #ebfff0 11.45%, #deffd6 100%)
        linear-gradient(155deg, #ebf6ff 11.45%, #d6f0ff 100%)
        linear-gradient(155deg, #f2ebff 11.45%, #e5d6ff 100%)
        linear-gradient(155deg, #fff5eb 11.45%, #ffe8c6 100%)
        linear-gradient(155deg, #ebfffc 11.45%, #d6fff7 100%)
        linear-gradient(155deg, #ffeef2 11.45%, #ffdde5 100%);
      @each $gradient in $color-list {
        $index: index($color-list, $gradient);
        &.bg-gradient-#{$index - 1} {
          background: $gradient;
        }
      }
    }
    .gap-1 {
      align-items: flex-start;
    }
    .gap-2 {
      align-items: flex-start;
    }
    .gap-3 {
      align-items: center;
    }
  }
  &-more {
    height: 300rpx;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 30%,
      rgba(255, 255, 255, 0.7) 70%,
      white 100%
    );
    border-radius: 0 0 20rpx 20rpx;
    position: absolute;
    z-index: -1;
    bottom: 20rpx;
    left: 0;
    transition: opacity 0.5s ease-in-out;
    opacity: 1;
    &-body {
      position: absolute;
      left: 50%;
      bottom: -48rpx;
      transform: translateX(-50%);
      cursor: pointer;
      transition: transform 0.3s ease;

      &:active {
        transform: translateX(-50%) scale(0.95);
      }

      &-bg {
        width: 150rpx;
        height: 50rpx;
      }
      &-icon {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-40%);
      }
    }
  }
}

.home_common-use {
  margin-top: 20rpx;
  &_header {
    padding-left: 20rpx;
    padding-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &_empty {
    background-color: white;
    margin: 20rpx;
    border-radius: 28rpx;
    min-height: 200rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    font-size: 30rpx;
  }
  &_body {
    ::v-deep .l-drag {
      overflow: visible;
      &__view {
        & .home_common-use_body-grid {
          padding: 0 20rpx 20rpx 20rpx;
          &[data-index='even'] {
            padding-right: 10rpx !important;
          }
          &[data-index='odd'] {
            padding-left: 10rpx !important;
          }
        }
      }
    }
    &-grid {
      padding: 0 20rpx 20rpx 20rpx;
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      &.item-hidden {
        max-height: 0px;
        padding-bottom: 0px;
        opacity: 0;
        transition:
          max-height 0.35s ease-in-out,
          padding-bottom 0.35s ease-in-out,
          opacity 0.3s ease-out;
      }
      &.item-visible {
        max-height: 330rpx;
        padding-bottom: 20rpx;
        opacity: 1;
        transition:
          padding-bottom 0.35s ease-in-out,
          opacity 0.3s ease-out;
      }
    }
    &-item {
      height: 100%;
      width: 100%;
      background-color: white;
      border-radius: 28rpx;
      padding: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      min-height: 300rpx;
      width: 100%;
      box-sizing: border-box;
      position: relative;
      &-bt {
        border-radius: 999999rpx;
        background: linear-gradient(103deg, #f1f8ff 6.83%, #f3efff 90.91%);
        width: 150rpx;
        height: 50rpx;
        padding: 6rpx 15rpx;
        text-align: center;
        font-size: 26rpx;
        &-font {
          background: linear-gradient(114deg, #4da3ff -18.56%, #7d4dff 108.66%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      &-del {
        position: absolute;
        top: -12rpx;
        right: -12rpx;
      }
    }
  }
  &_collapse {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-block: 20rpx 30rpx;
    &-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10rpx;
    }
  }
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f2edff;
    width: 60rpx;
    height: 60rpx;
  }
}

.home_recent {
  background-color: white;
  padding: 20rpx;
  &-tab {
    padding-top: 30rpx;
  }
  ::v-deep .u-swipe-action-item__right {
    border-radius: 0 20rpx 20rpx 0;
    overflow: hidden;
  }
}

.home_common-use_body-item {
  height: 100%;
  width: 100%;
  background-color: white;
  border-radius: 28rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 300rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;

  &.item-content-hidden {
    transform: scale(0.5);
    opacity: 0;
    transition:
      transform 0.3s ease-in-out,
      opacity 0.3s ease-out;
  }

  &.item-content-visible {
    transform: scale(1);
    opacity: 1;
    transition:
      transform 0.3s ease-in-out,
      opacity 0.3s ease-out;
  }
}

.icon-rotated {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
</style>
