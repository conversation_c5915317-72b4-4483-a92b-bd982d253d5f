<script setup lang="ts">
import { ref, onMounted, reactive, watch, useSlots, nextTick } from 'vue';

interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
  realTotal: number;
}

interface PageParams {
  current: number;
  size: number;
  [key: string]: any;
}

interface Props<T> {
  // 请求接口方法
  fetchMethod: (params: PageParams) => Promise<PageResponse<T>>;
  // 每页大小
  pageSize?: number;
  // 额外的请求参数
  extraParams?: Record<string, any>;
  // 是否自动加载第一页
  autoLoad?: boolean;
  // 数据处理函数
  processData?: (data: T[]) => T[];
  // 是否启用上拉加载
  enablePullUp?: boolean;
  // 空数据提示文本
  emptyText?: string;
  // 加载中提示文本
  loadingText?: string;
  // 无更多数据提示文本
  noMoreText?: string;

  // 其他更多加载方法realTotal判断
  realTotalWay?: boolean;
  // 禁用请求
  disabledRequest?: boolean;
}

const props = withDefaults(defineProps<Props<any>>(), {
  pageSize: 10,
  extraParams: () => ({}),
  autoLoad: true,
  processData: (data: any[]) => data,
  enablePullUp: true,
  emptyText: '暂无数据',
  loadingText: '加载中...',
  noMoreText: '没有更多了',
  disabledRequest: false,
});

// 列表数据
const list = ref<any[]>([]);
// 分页参数
const pageParams = reactive<PageParams>({
  current: 1,
  size: props.pageSize,
  ...props.extraParams,
});
// 加载状态
const loading = ref(false);
// 是否还有更多数据
const hasMore = ref(true);
// 是否为首次加载
const isFirstLoad = ref(true);
// 获取插槽
const slots = useSlots();

// 加载数据
const loadData = async (isRefresh = false) => {
  if (loading.value || props.disabledRequest) return;

  // 如果是刷新，重置页码
  if (isRefresh) {
    pageParams.current = 1;
    list.value = [];
    hasMore.value = true;
  }

  loading.value = true;

  try {
    const response = await props.fetchMethod(pageParams);

    // 检查响应是否有效
    if (!response || !Array.isArray(response.records)) {
      throw new Error('无效的响应数据');
    }

    // 处理数据，使用Try-Catch包装处理逻辑，防止处理函数异常导致整个请求失败
    let processedData = [];
    try {
      processedData = props.processData(response.records || []);
    } catch (procError) {
      console.error('数据处理失败:', procError);
      processedData = response.records || [];
    }

    if (isRefresh) {
      list.value = processedData;
    } else {
      // 使用展开操作符添加新数据，避免直接修改引用
      list.value = [...list.value, ...processedData];
    }

    // 判断是否还有更多数据
    if (!props.realTotalWay) {
      hasMore.value = pageParams.current < response.pages;
    } else {
      hasMore.value = response.realTotal >= response.size;
    }

    // 更新页码
    if (hasMore.value) {
      pageParams.current += 1;
    }

    isFirstLoad.value = false;
  } catch (error) {
    console.error('加载数据失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    });
  } finally {
    // 确保无论如何都更新loading状态
    loading.value = false;
  }
};

// 刷新数据 - 添加防抖，避免频繁调用
let refreshTimer: number | null = null;
const refresh = () => {
  // 取消之前的定时器
  if (refreshTimer !== null) {
    clearTimeout(refreshTimer);
  }

  // 清空现有数据
  list.value = [];
  hasMore.value = true;
  isFirstLoad.value = true;

  // 重置页码
  pageParams.current = 1;

  // 创建新的定时器
  refreshTimer = setTimeout(() => {
    loadData(true);
    refreshTimer = null;
  }, 50) as unknown as number;
};

// 监听参数变化，重新加载数据
watch(
  () => props.extraParams,
  (newVal, oldVal) => {
    console.log('newVal', newVal);
    console.log('oldVal', oldVal);
    if (!newVal) return;

    // 只有当参数有实际变化时才更新并重新加载
    const hasChanged = !oldVal || Object.keys(newVal).some(key => newVal[key] !== oldVal?.[key]);

    if (hasChanged) {
      // 更新参数
      Object.keys(newVal).forEach(key => {
        pageParams[key] = newVal[key];
      });

      // 重新加载数据
      if (!props.disabledRequest) {
        refresh();
      }
    }
  },
  { deep: true }
);

// 监听disabledRequest变化
watch(
  () => props.disabledRequest,
  (newVal, oldVal) => {
    // 如果从禁用状态变为启用状态，并且列表为空，则加载数据
    if (oldVal === true && newVal === false && list.value.length === 0) {
      refresh();
    }
  }
);

// 滚动到底部加载更多
const onScrollToLower = () => {
  if (hasMore.value && !loading.value && props.enablePullUp && !props.disabledRequest) {
    loadData();
  }
};

// 自动加载首页数据
onMounted(() => {
  if (props.autoLoad && !props.disabledRequest) {
    nextTick(() => {
      setTimeout(() => {
        loadData(true);
      }, 50);
    });
  }
});

// 暴露方法
defineExpose({
  refresh,
  loadData,
  list,
});
</script>

<template>
  <view class="lk-page-list">
    <!-- <LkLoading :loading="isFirstLoad && loading"> -->
    <!-- <text v-if="loading">正在加载...</text> -->
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="onScrollToLower"
      :scroll-top="0"
      :lower-threshold="50"
      :refresher-enabled="false"
      :bounces="true"
      :show-scrollbar="true"
    >
      <!-- 内容区域 -->
      <view class="list-content">
        <slot :list="list"></slot>
      </view>

      <!-- 空状态 -->
      <template v-if="!list.length">
        <slot name="empty" v-if="slots.empty"></slot>
        <view class="empty-state" v-else>
          <u-empty :text="emptyText" mode="data"></u-empty>
        </view>
      </template>

      <!-- 底部状态 -->
      <view v-if="list.length > 0" class="list-footer">
        <view v-if="loading" class="loading-state">
          <u-loading-icon size="18" mode="circle"></u-loading-icon>
          <text>{{ loadingText }}</text>
        </view>
        <view v-else-if="!hasMore" class="no-more-state">
          <text>{{ noMoreText }}</text>
        </view>
        <view v-else class="pull-up-hint">
          <text>上拉加载更多</text>
        </view>
      </view>
    </scroll-view>
    <!-- </LkLoading> -->
  </view>
</template>

<style lang="scss" scoped>
.lk-page-list {
  height: 100%;

  .scroll-container {
    height: 100%;
  }

  .list-content {
    min-height: 100rpx;
  }

  .empty-state {
    padding: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .list-footer {
    padding-bottom: 30rpx;
    text-align: center;
    background-color: #fff;

    .loading-state,
    .no-more-state,
    .pull-up-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #86909c;
      font-size: 24rpx;

      text {
        margin-left: 10rpx;
      }
    }
  }
}
</style>
