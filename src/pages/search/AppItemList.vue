<script setup lang="ts">
import { ref } from 'vue';
import { getMyAppPage } from '@/api/app';
import type { AppListItemType } from '@/types/api/app';
import { AppTypeEnum } from '@/constants/api/app';
import { DataSourceMap, DataSource } from '@/constants/api';
import { setCommonApp, rmCommonApp } from '@/api/userCommon';
const props = defineProps<{
  keyword: string;
}>();
const uToastRef = ref();
// 使用真实API获取应用列表
const fetchAppList = async (params: any) => {
  // 调用真实接口
  return await getMyAppPage({
    ...params,
    keyword: props.keyword,
  });
};

// 处理数据
const processAppData = (data: AppListItemType[]) => {
  return data.map(item => ({
    ...item,
    icon: item.avatarUrl || '/static/app/test-icon.png',
    description: item.intro || '暂无描述',
    isOfficial: item.type === AppTypeEnum.simple,
  }));
};

// 监听关键词变化的处理已由 watchEffect 在 LkPageList 中处理

const handleAddApp = (app: AppListItemType) => {
  setCommonApp({ id: app.id }).then(() => {
    app.isCommonApp = 1;
    uToastRef.value.show({
      message: '已添加到我的应用',
      type: 'success',
    });
  });
};

const handleRemoveApp = (app: AppListItemType) => {
  rmCommonApp({ id: app.id }).then(() => {
    app.isCommonApp = 0;
    uToastRef.value.show({
      message: '已移除常用应用',
      type: 'success',
    });
  });
};
</script>

<template>
  <view class="app-list">
    <LkPageList
      :fetch-method="fetchAppList"
      :extra-params="{ keyword: keyword }"
      :process-data="processAppData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-if="list.length">
          <view v-for="item in list" :key="item.finalAppId" class="list-item">
            <view class="item-main">
              <image :src="item.icon" class="app-icon" />
              <view class="item-info">
                <view class="item-name">{{ item.name }}</view>
                <view class="item-desc">{{ item.description }}</view>
                <view class="item-tag">
                  <image :src="DataSourceMap[item.source as DataSource].imgSrc" />
                  <text>{{ DataSourceMap[item.source as DataSource].label }}</text>
                </view>
              </view>
              <view class="add-btn" v-if="!item.isCommonApp" @click="handleAddApp(item)">
                <image src="/static/search/plus.svg" />
              </view>
              <view class="remove-btn" v-else @click="handleRemoveApp(item)">
                <image src="/static/search/check.svg" />
              </view>
            </view>
          </view>
        </view>
        <template v-else>
          <u-empty text="暂无相关应用" mode="list" />
        </template>
      </template>
    </LkPageList>

    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>

<style lang="scss">
.app-list {
  height: 100%;

  .list-item {
    background: #fff;
    border-radius: 20rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
    border: 1rpx solid #f4f4f4;

    .item-main {
      display: flex;
      gap: 20rpx;
      align-items: center;

      .app-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
        flex-shrink: 0;
      }

      .item-info {
        flex: 1;
        overflow: hidden;
      }

      .item-name {
        font-size: 32rpx;
        color: #1d2129;
        margin-bottom: 8rpx;
        font-weight: 500;
      }

      .item-desc {
        font-size: 24rpx;
        color: #86909c;
        margin-bottom: 8rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .item-tag {
        display: flex;
        align-items: center;
        gap: 8rpx;

        image {
          width: 28rpx;
          height: 28rpx;
        }

        text {
          font-size: 24rpx;
          color: #86909c;
        }
      }

      .add-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
      .remove-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        background: #f2f3f5;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        image {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }
}
</style>
