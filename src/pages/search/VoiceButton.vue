<script setup lang="ts">
import { ref, watch } from 'vue';
import { useSpeech } from '@/hooks/useSpeech';
import PermissionModal from '@/components/LKpermissionModal/index.vue';

// 定义组件的 props
interface Props {
  maxDuration?: number; // 最大录音时长，默认60秒
  showToast?: boolean; // 是否显示错误提示，默认true
  showPermissionModal?: boolean; // 是否显示权限模态框，默认true
}

const props = withDefaults(defineProps<Props>(), {
  maxDuration: 60000,
  showToast: true,
  showPermissionModal: true,
});

// 定义组件的 emits
const emit = defineEmits<{
  result: [text: string]; // 语音识别结果
  error: [error: string]; // 错误信息
  recordStart: []; // 开始录音
  recordEnd: []; // 结束录音
  recordCancel: []; // 取消录音
  permissionDenied: []; // 权限被拒绝
}>();

const uToastRef = ref();
const isShowVoicePermissionModal = ref(false); // 控制权限模态框的显示

// 检查并请求语音权限
const checkAndRequestVoicePermission = async (): Promise<boolean> => {
  return new Promise(resolve => {
    // #ifdef APP-PLUS
    // 检查当前权限状态
    const appAuthorizeSetting = uni.getAppAuthorizeSetting();
    const microphoneAuthorized = appAuthorizeSetting.microphoneAuthorized;

    if (microphoneAuthorized === 'authorized') {
      resolve(true);
      return;
    }

    if (microphoneAuthorized === 'denied') {
      // 用户已拒绝授权，提示用户去设置中开启
      showPermissionModal();
      resolve(false);
      return;
    }

    // 'not determined' 或其他情况，发起授权请求
    uni.authorize({
      scope: 'scope.record',
      success: () => {
        resolve(true);
      },
      fail: () => {
        // 用户在授权弹窗中拒绝
        showPermissionModal();
        resolve(false);
      },
    });
    // #endif

    // #ifndef APP-PLUS
    // 非App端，直接认为有权限或不处理，具体逻辑视平台需求而定
    console.warn('语音权限检查仅在App端生效');
    resolve(true); // 默认返回 true，以便在非App平台也能继续流程
    // #endif
  });
};

// 显示权限模态框
const showPermissionModal = () => {
  if (props.showPermissionModal) {
    isShowVoicePermissionModal.value = true;
  }
  emit('permissionDenied');
};

// 权限模态框的取消操作
const handlePermissionModalCancel = () => {
  isShowVoicePermissionModal.value = false;
  console.log('用户取消语音权限授权');
};

// 权限模态框的确认操作 (前往设置)
const handlePermissionModalConfirm = () => {
  isShowVoicePermissionModal.value = false;
  // #ifdef APP-PLUS
  uni.openAppAuthorizeSetting({
    success: resOpen => {
      console.log('打开设置页面成功', resOpen);
    },
    fail: err => {
      console.error('打开设置页面失败', err);
      uni.showToast({
        title: '无法自动跳转，请手动前往系统设置开启权限',
        icon: 'none',
        duration: 3000,
      });
    },
  });
  // #endif
  // #ifndef APP-PLUS
  console.warn('打开应用设置功能仅在App端支持');
  // #endif
};

// 使用语音hook
const {
  isRecording,
  isTranscripting,
  recognizedText,
  errorMessage,
  startRecord,
  stopRecord,
  cancelRecord,
  hasSound,
} = useSpeech({
  maxDuration: props.maxDuration,
  onResult: text => {
    emit('result', text);
  },
  onError: error => {
    emit('error', error);
  },
});

// 监听错误信息，如果需要显示Toast
watch(errorMessage, newError => {
  if (newError && props.showToast && uToastRef.value) {
    uToastRef.value.show({
      message: newError,
      type: 'info',
    });
  }
});

// 语音按钮事件处理
const handleTouchStart = async (e: any) => {
  // 在开始录音前检查权限
  // #ifdef APP-PLUS
  const hasPermission = await checkAndRequestVoicePermission();
  if (!hasPermission) {
    return;
  }
  // #endif

  startRecord();
  emit('recordStart');
};

const handleTouchEnd = (e: any) => {
  stopRecord();
  emit('recordEnd');
};

const handleTouchCancel = (e: any) => {
  cancelRecord();
  emit('recordCancel');
};

const handleTouchMove = (e: any) => {
  // 获取触摸点相对于按钮的位置
  const touch = e.touches[0];

  // 获取按钮的位置信息
  const query = uni.createSelectorQuery();
  query
    .select('.voice-btn-inner')
    .boundingClientRect((data: any) => {
      if (!data) return;

      // 判断触摸点是否在按钮范围内
      if (
        touch.clientX < data.left ||
        touch.clientX > data.right ||
        touch.clientY < data.top ||
        touch.clientY > data.bottom
      ) {
        console.log('手指移出按钮区域，取消录音');
        cancelRecord();
        emit('recordCancel');
      }
    })
    .exec();
};

// 暴露组件状态给父组件
defineExpose({
  isRecording,
  isTranscripting,
  recognizedText,
  errorMessage,
  hasSound,
  checkAndRequestVoicePermission,
});
</script>

<template>
  <view class="voice-btn">
    <view
      class="voice-btn-inner"
      :class="{ 'voice-btn-active': isRecording }"
      @touchstart.prevent.stop="handleTouchStart"
      @touchend.prevent.stop="handleTouchEnd"
      @touchcancel.prevent.stop="handleTouchCancel"
      @touchmove.prevent.stop="handleTouchMove"
    >
      <view class="voice-btn-content">
        <image class="voice-btn-icon" :class="{ 'voice-btn-icon-active': isRecording }" />
      </view>
    </view>
    <text>{{ isRecording ? '松开完成' : '按住说话' }}</text>
    <text v-if="isTranscripting" class="recognizing-text">识别中...</text>
    <text v-if="hasSound" class="volume-text">检测到声音</text>
  </view>

  <!-- Toast 提示组件 -->
  <LkToast v-if="showToast" ref="uToastRef"></LkToast>

  <!-- 权限模态框 -->
  <PermissionModal
    v-model:show="isShowVoicePermissionModal"
    title="语音权限未开启"
    content="检测到手机设置中未对APP 开启语音授权，请先在手机设置开启。"
    cancel-text="取消"
    confirm-text="前往设置"
    @cancel="handlePermissionModalCancel"
    @confirm="handlePermissionModalConfirm"
  />
</template>

<style lang="scss" scoped>
.voice-btn {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  z-index: 100;

  &-inner {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
      0 5rpx 10rpx rgba(96, 87, 182, 0.1),
      0 8rpx 20rpx rgba(96, 87, 182, 0.06),
      0 3rpx 28rpx rgba(96, 86, 181, 0.19);
    transition: all 0.2s ease;
    position: relative;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;

    &.voice-btn-active {
      transform: scale(1.1);
      box-shadow:
        0 8rpx 16rpx rgba(96, 87, 182, 0.15),
        0 12rpx 28rpx rgba(96, 87, 182, 0.1),
        0 5rpx 38rpx rgba(96, 86, 181, 0.25);
    }

    .voice-btn-content {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 1;

      .voice-btn-icon {
        width: 60rpx;
        height: 60rpx;
        position: relative;
        z-index: 2;
        background-image: url('/static/search/startVoice.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        &.voice-btn-icon-active {
          background-image: url('/static/search/voiceIng.svg');
        }
      }
    }
  }

  text {
    font-size: 28rpx;
    color: #86909c;
  }

  .recognizing-text {
    color: #4da3ff;
    font-size: 24rpx;
    margin-top: -6rpx;
  }

  .volume-text {
    color: #4da3ff;
    font-size: 24rpx;
    margin-top: 4rpx;
  }
}
</style>
