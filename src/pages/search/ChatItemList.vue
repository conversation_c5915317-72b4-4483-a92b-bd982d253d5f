<script setup lang="ts">
import { ref, watch } from 'vue';
import { getChatHistories } from '@/api/chat';
import type { ChatHistoryItemType } from '@/types/api/chat';
import type { PagingData } from '@/types/api/common';

const props = defineProps<{
  keyword: string;
}>();

// 使用 LkPageList 替代原有的加载逻辑
const fetchChatHistory = async (params: any) => {
  // 调用真实接口
  return await getChatHistories({
    ...params,
    keyword: props.keyword,
  });
};

// 处理数据和关键字高亮
const processChatData = (data: ChatHistoryItemType[]) => {
  return data.map(item => {
    // 添加高亮处理
    let title = item.title;
    if (props.keyword && title) {
      // 替换所有匹配的关键字为带有高亮样式的文本
      title = highlightKeyword(title, props.keyword);
    }

    return {
      ...item,
      titleHtml: title,
      date: new Date(item.updateTime || '').toLocaleString(),
    };
  });
};

// 关键字高亮处理函数
const highlightKeyword = (text: string, keyword: string): string => {
  if (!keyword.trim()) return text;
  // 使用正则表达式进行全局匹配，忽略大小写
  const reg = new RegExp(keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
  return text.replace(reg, match => `<span class="highlight">${match}</span>`);
};

// 对话点击处理
const handleChatClick = (chatId: string) => {
  uni.navigateTo({
    url: `/pages/chat/index?chatId=${chatId}`,
  });
};
</script>

<template>
  <view class="chat-list">
    <LkPageList
      :fetch-method="fetchChatHistory"
      :extra-params="{ keyword: keyword }"
      :process-data="processChatData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-if="list.length">
          <view
            v-for="item in list"
            :key="item.chatId"
            class="list-item"
            @click="handleChatClick(item.chatId)"
          >
            <view class="item-title">
              <rich-text class="title-text" :nodes="item.titleHtml"></rich-text>
            </view>
            <view class="item-date">{{ item.date }}</view>
          </view>
        </view>
        <template v-else>
          <u-empty text="暂无相关对话" mode="message" />
        </template>
      </template>
    </LkPageList>
  </view>
</template>

<style lang="scss">
.chat-list {
  height: 100%;

  .list {
    &-item {
      background: #fff;
      border-radius: 20rpx;
      padding: 24rpx;
      margin-bottom: 24rpx;
      box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
      border: 1rpx solid #f4f4f4;

      .item-title {
        margin-bottom: 22rpx;
        :deep(div) {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
        }

        .title-text {
          font-size: 32rpx;
          color: #1d2129;
          line-height: 48rpx;
        }
      }

      .item-date {
        font-size: 24rpx;
        color: #86909c;
      }
    }
  }
}

/* 高亮样式 */
.highlight {
  color: #1a5eff;
}
</style>
