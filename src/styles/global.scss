/* 全局样式 */
* {
  outline: none;
  box-sizing: border-box;
}

/* u-toast 样式覆盖 */
.u-toast {
  .u-toast__content {
    border-radius: 28rpx !important;
    background-color: white !important;
    box-shadow:
      0 -2rpx 32rpx 0 rgba(0, 0, 0, 0.14),
      0 2rpx 20rpx 0 rgba(0, 0, 0, 0.05),
      0 4rpx 14rpx -2rpx rgba(0, 0, 0, 0.12) !important;
    padding: 36rpx 28rpx !important;
    transform: translateY(-38vh) !important;
  }

  .u-toast__content__text {
    color: #000 !important;
  }

  .u-icon__icon {
    font-size: 40rpx !important;
    margin-right: 15rpx !important;
    color: #7d4dff !important;
  }

  .u-icon__icon--success {
    color: #009968 !important;
  }

  .u-type-success {
    color: #000 !important;
    border-color: transparent !important;
    background: white !important;
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
