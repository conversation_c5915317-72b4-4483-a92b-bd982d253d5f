export interface LoginResponse {
  accessToken: string;
  source: string;
  userId: string;
  username: string;
  account: string;
  phone: string;
  avatar: string;
  roleId: string;
  roleType: string;
  roleName: string;
  tmbId: string;
  tenantId: string;
  status: string;
  menuCodes: string[];
  chatId: string;
  isSso: number;
  firstLogin: boolean;
  /** 性别 0: 未知, 1: 男, 2: 女 */
  gender: number;
}

export interface TenantInfo {
  accessKey: string;
  account: string;
  username: string;
  tenantId: string;
  tenantAvatar: string;
  tenantName: string;
  industry: number;
  isDefault: string;
}
