<template>
  <view class="smart-step-guide" :class="[theme]">
    <view v-if="visible" class="guide-container">
      <!-- 透明的全屏覆盖层，阻止所有点击和滚动事件 -->
      <view
        class="event-blocker"
        @touchmove.prevent.stop
        @click.stop
        @touchstart.prevent.stop
        @scroll.prevent.stop
      >
      </view>

      <!-- 高亮区域与蒙版 -->
      <view
        class="highlight-overlay"
        :style="{
          left: targetPosition.left + 'px',
          top: targetPosition.top + 'px',
          width: targetPosition.width + 'px',
          height: targetPosition.height + 'px',
          'border-radius': targetBorderRadius,
        }"
      >
      </view>

      <!-- 引导提示框 -->
      <view class="guide-box" :style="guideBoxStyle" :class="guideBoxClass">
        <view class="guide-box-content">
          <view class="guide-box-content-lollipop">
            <view class="guide-box-content-lollipop-detail" />
          </view>
          <view class="guide-box-content-text">{{ currentStep?.content || '' }}</view>
        </view>
        <!-- <view class="guide-box-bt prev" @click="prevStep">上一步</view> -->
        <view class="guide-box-bt next" @click.stop="nextStep">{{
          isLastStep ? '知道了' : '下一步'
        }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, getCurrentInstance } from 'vue';

interface Step {
  target: string;
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  validate?: () => boolean;
}

interface Position {
  left: number;
  top: number;
  width: number;
  height: number;
}

interface NodeInfo {
  left: number;
  top: number;
  width: number;
  height: number;
  right: number;
  bottom: number;
}

const props = defineProps({
  steps: {
    type: Array as () => Step[],
    default: () => [],
  },
  theme: {
    type: String,
    default: 'light',
  },
  skipEnabled: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['skip', 'complete']);

const visible = ref(false);
const currentIndex = ref(0);
const targetPosition = ref<Position>({
  left: 0,
  top: 0,
  width: 0,
  height: 0,
});

// 保存目标元素的边框圆角
const targetBorderRadius = ref('0');

// 记录页面禁用滚动前的位置
const scrollTop = ref(0);

// 获取当前环境
const platform = ref('');
onMounted(() => {
  // #ifdef APP-PLUS
  platform.value = 'app';
  // #endif

  // #ifdef H5
  platform.value = 'h5';
  // #endif

  // #ifdef MP
  platform.value = 'mp';
  // #endif
});

const currentStep = computed((): Step | null => {
  return props.steps[currentIndex.value] || null;
});

const isLastStep = computed((): boolean => {
  return currentIndex.value === props.steps.length - 1;
});

const guideBoxStyle = computed((): string => {
  const position = currentStep.value?.position || 'bottom';
  const { left, top, width, height } = targetPosition.value;

  // 获取屏幕尺寸
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const screenHeight = systemInfo.windowHeight;

  // 引导框尺寸和安全边距
  const guideBoxWidth = 240;
  const guideBoxHeight = 120;
  const margin = 12;
  const safeMargin = 10;

  // 计算水平居中位置
  let boxLeft = Math.min(
    Math.max(left + width / 2, guideBoxWidth / 2 + margin),
    screenWidth - guideBoxWidth / 2 - margin
  );

  // 目标元素上下边界
  const targetTop = top;
  const targetBottom = top + height;
  const lollipopOffset = 50; // 棒棒糖连接线额外偏移量

  // 计算垂直位置
  let boxTop = 0;
  if (position === 'top') {
    // 上方位置，考虑棒棒糖的连接线
    if (targetTop >= guideBoxHeight + safeMargin + lollipopOffset) {
      boxTop = targetTop - guideBoxHeight - lollipopOffset;
    } else {
      // 空间不足切换到下方
      boxTop = targetBottom + safeMargin;
    }
  } else {
    // 默认下方位置
    if (screenHeight - targetBottom >= guideBoxHeight + safeMargin + lollipopOffset) {
      boxTop = targetBottom + safeMargin;
    } else if (targetTop >= guideBoxHeight + safeMargin + lollipopOffset) {
      // 下方空间不足切换到上方
      boxTop = targetTop - guideBoxHeight - lollipopOffset;
    } else {
      // 上下空间都不足，选择较大的一侧
      const topSpace = targetTop;
      const bottomSpace = screenHeight - targetBottom;

      if (topSpace > bottomSpace) {
        boxTop = Math.max(margin, targetTop - guideBoxHeight - lollipopOffset);
      } else {
        boxTop = Math.min(screenHeight - guideBoxHeight - margin, targetBottom + safeMargin);
      }
    }
  }

  // 确保不超出屏幕上下边界
  boxTop = Math.min(Math.max(boxTop, margin), screenHeight - guideBoxHeight - margin);

  return `left: ${boxLeft}px; top: ${boxTop}px;`;
});

const guideBoxClass = computed((): string => {
  const position = currentStep.value?.position || 'bottom';
  const { top, height } = targetPosition.value;
  const systemInfo = uni.getSystemInfoSync();
  const screenHeight = systemInfo.windowHeight;
  const guideBoxHeight = 120;
  const margin = 12;

  switch (position) {
    case 'top':
      return top - margin - guideBoxHeight < margin ? 'arrow-top' : 'arrow-bottom';
    case 'bottom':
    default:
      return top + height + margin + guideBoxHeight > screenHeight - margin
        ? 'arrow-bottom'
        : 'arrow-top';
  }
});

// 禁用页面滚动 - 跨平台实现
const disableScroll = () => {
  try {
    // 记录当前滚动位置
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });

    // H5平台
    // #ifdef H5
    const bodyEl = document.documentElement || document.body;
    bodyEl.style.overflow = 'hidden';
    bodyEl.style.position = 'fixed';
    bodyEl.style.width = '100%';
    // #endif

    // 小程序平台
    // #ifdef MP
    // 使用catchtouchmove阻止滚动，依赖模板中的@touchmove.prevent
    // #endif

    // APP平台 - 禁用手势
    // #ifdef APP-PLUS
    try {
      const webview = plus.webview.currentWebview();
      if (webview) {
        webview.setStyle({
          bounce: 'none',
        });
      }
    } catch (e) {
      console.warn('APP禁用滚动失败:', e);
    }
    // #endif
  } catch (e) {
    console.warn('禁用滚动失败:', e);
  }
};

// 恢复页面滚动 - 跨平台实现
const enableScroll = () => {
  try {
    // H5平台
    // #ifdef H5
    const bodyEl = document.documentElement || document.body;
    bodyEl.style.overflow = '';
    bodyEl.style.position = '';
    bodyEl.style.width = '';
    // #endif

    // APP平台 - 恢复手势
    // #ifdef APP-PLUS
    try {
      const webview = plus.webview.currentWebview();
      if (webview) {
        webview.setStyle({
          bounce: 'vertical',
        });
      }
    } catch (e) {
      console.warn('APP恢复滚动失败:', e);
    }
    // #endif
  } catch (e) {
    console.warn('恢复滚动失败:', e);
  }
};

// 开始引导
const start = async () => {
  // 重置状态
  currentIndex.value = 0;
  targetPosition.value = {
    left: 0,
    top: 0,
    width: 0,
    height: 0,
  };
  targetBorderRadius.value = '0';

  // 延迟更新位置，确保DOM已完全渲染
  await updateTargetPosition();

  // 确保位置更新后再显示
  nextTick(() => {
    // 禁用页面滚动
    disableScroll();
    visible.value = true;
  });
};

// 结束引导
const stop = () => {
  // 隐藏引导
  visible.value = false;

  // 恢复页面滚动
  enableScroll();

  // 等待过渡动画完成后再重置位置
  setTimeout(() => {
    currentIndex.value = 0;
    targetPosition.value = {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
    };
    targetBorderRadius.value = '0';
  }, 300); // 过渡动画时长
};

const nextStep = async () => {
  const step = currentStep.value;
  if (step?.validate && !step.validate()) {
    // 验证失败，不进行下一步
    return;
  }
  if (isLastStep.value) {
    complete();
  } else {
    currentIndex.value++;
    await updateTargetPosition();
  }
};

const prevStep = async () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    await updateTargetPosition();
  }
};

const skipGuide = () => {
  stop();
  emit('skip');
};

const complete = () => {
  stop();
  emit('complete');
};

// 获取元素的计算样式
const getElementComputedStyle = async (selector: string): Promise<any> => {
  return new Promise(resolve => {
    try {
      const query = uni.createSelectorQuery();

      // 在组件内查询
      // #ifndef MP-ALIPAY
      if (getCurrentInstance()) {
        query.in(getCurrentInstance());
      }
      // #endif

      query
        .select(selector)
        .fields(
          {
            computedStyle: [
              'borderRadius',
              'borderTopLeftRadius',
              'borderTopRightRadius',
              'borderBottomLeftRadius',
              'borderBottomRightRadius',
            ],
          },
          res => {
            resolve(res);
          }
        )
        .exec();
    } catch (e) {
      console.error('获取样式失败:', e);
      resolve(null);
    }
  });
};

// 更新目标位置 - 跨平台实现
const updateTargetPosition = async () => {
  const step = currentStep.value;
  if (!step) return;

  try {
    // 使用简单的延迟确保DOM已渲染
    await new Promise(resolve => setTimeout(resolve, 100));

    // 使用uni选择器
    const query = uni.createSelectorQuery();

    // 在组件内查询
    // #ifndef MP-ALIPAY
    if (getCurrentInstance()) {
      query.in(getCurrentInstance());
    }
    // #endif

    // 在页面范围内查询
    const result = await new Promise<NodeInfo | null>(resolve => {
      query
        .select(step.target)
        .boundingClientRect(data => {
          if (data && !Array.isArray(data)) {
            // 确保data是单个NodeInfo对象
            resolve({
              left: data.left || 0,
              top: data.top || 0,
              width: data.width || 0,
              height: data.height || 0,
              right: data.right || 0,
              bottom: data.bottom || 0,
            } as NodeInfo);
          } else {
            resolve(null);
          }
        })
        .exec();
    });

    // 获取目标元素的计算样式
    const styleInfo = await getElementComputedStyle(step.target);
    if (styleInfo) {
      // 先检查四个角的圆角是否一致
      const topLeft = styleInfo.borderTopLeftRadius || '0';
      const topRight = styleInfo.borderTopRightRadius || '0';
      const bottomLeft = styleInfo.borderBottomLeftRadius || '0';
      const bottomRight = styleInfo.borderBottomRightRadius || '0';

      // 如果四个角都是一致的，则使用一致的圆角
      if (topLeft === topRight && topLeft === bottomLeft && topLeft === bottomRight) {
        targetBorderRadius.value = topLeft; // 四个角一致
      } else {
        targetBorderRadius.value = `${topLeft} ${topRight} ${bottomRight} ${bottomLeft}`;
      }

      // 如果没有任何具体的圆角设置，则使用整数borderRadius
      if (targetBorderRadius.value === '0 0 0 0' && styleInfo.borderRadius) {
        targetBorderRadius.value = styleInfo.borderRadius;
      }

      // 如果没有设置圆角，则使用默认值
      if (targetBorderRadius.value === '0' || targetBorderRadius.value === '0 0 0 0') {
        targetBorderRadius.value = '4px';
      }
    } else {
      targetBorderRadius.value = '4px'; // 默认圆角
    }

    if (result) {
      // 声明一个统一的位置变量
      let newPosition: Position;

      // 小程序中需要特殊处理坐标
      // #ifdef MP
      const systemInfo = uni.getSystemInfoSync();
      const pixelRatio = systemInfo.pixelRatio || 1;

      // 计算实际位置，确保精确显示
      newPosition = {
        left: Math.floor(result.left),
        top: Math.floor(result.top),
        width: Math.floor(result.width),
        height: Math.floor(result.height),
      };
      // #endif

      // 其他平台正常处理
      // #ifndef MP
      newPosition = {
        left: result.left,
        top: result.top,
        width: result.width,
        height: result.height,
      };
      // #endif

      // 更新位置前先验证数据的有效性
      if (newPosition.width > 0 && newPosition.height > 0) {
        targetPosition.value = newPosition;

        // 确保视图更新
        await nextTick();
      } else {
        console.warn('计算得到的尺寸无效:', newPosition);
      }
    } else {
      console.warn('未找到目标元素:', step.target);
    }
  } catch (error) {
    console.error('查询元素位置失败:', error);
  }
};

// 确保组件销毁时恢复滚动
onUnmounted(() => {
  if (visible.value) {
    enableScroll();
  }
});

// 暴露公共方法给父组件
defineExpose({
  start,
  stop,
  nextStep,
  prevStep,
});
</script>

<style lang="scss" scoped>
@import '@/styles/theme.scss';

.smart-step-guide {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;

  .guide-container {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    pointer-events: none;
  }

  // 事件阻挡层样式
  .event-blocker {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: transparent;
    pointer-events: auto; // 重要：捕获所有点击事件
  }

  .highlight-overlay {
    position: fixed;
    z-index: 2;
    pointer-events: none;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7); /* 创建蒙版效果 */
  }

  .guide-box {
    position: absolute;
    z-index: 10001;
    transform: translateX(-50%);
    pointer-events: auto;
    &-content {
      &-text {
        color: #fff;
        background-color: $brand5;
        padding: 20rpx;
        border-radius: 30rpx;
        width: 80vw;
      }
    }
    &-bt {
      color: $brand6;
      border: 1px solid $brand5;
      background-color: #fff;
      border-radius: 99999rpx;
      padding: 12rpx 24rpx;
      position: absolute;
      bottom: -50px;
      pointer-events: auto;
    }
    .next {
      right: 0;
    }
    .prev {
      left: 0;
    }
    &.arrow-top {
      .guide-box-content-lollipop {
        height: 90rpx;
        padding-inline: 30rpx;
        &-detail {
          position: relative;
          width: 12rpx; // 略微增大圆点
          height: 12rpx;
          background: white;
          border-radius: 50%;
          float: right;
          box-shadow: 0 0 5px rgba(255, 255, 255, 0.8); // 添加发光效果
          &::after {
            content: '';
            position: absolute;
            bottom: -80rpx;
            left: 50%;
            transform: translateX(-40%); // 居中对齐连接线
            width: 1px; // 稍微加粗连接线
            height: 82rpx;
            background: white;
            box-shadow: 0 0 3px rgba(255, 255, 255, 0.5); // 轻微发光效果
          }
        }
      }
    }
    &.arrow-bottom {
      .guide-box-content-lollipop {
        padding-inline: 30rpx;
        position: absolute;
        bottom: -155rpx;
        left: 50%;
        transform: translateX(-50%);
        &-detail {
          position: relative;
          width: 12rpx; // 略微增大圆点
          height: 12rpx;
          background: white;
          border-radius: 50%;
          float: right;
          box-shadow: 0 0 5px rgba(255, 255, 255, 0.8); // 添加发光效果
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%); // 改为居中对齐
            width: 1px; // 稍微加粗连接线
            height: 155rpx;
            background: white;
            box-shadow: 0 0 3px rgba(255, 255, 255, 0.5); // 轻微发光效果
          }
        }
      }
    }
  }
}

/* 主题样式 */
.dark {
  .guide-box {
    background-color: #333;
  }
}
</style>
