<script setup>
import { ref, watch, computed } from 'vue';
import LkSvg from '../svg/index';
import { getBaseUrl } from '../../common/ai/url';
import { checkPermission } from '@/utils/permission';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';

const lkDatabasePopupRef = ref(null);
const bizType = ref('1');
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['select-type', 'update:modelValue', 'submit']);

const items = ref([
  { type: 'camera', text: '相机', icon: '/static/uploadall/camera.svg' },
  { type: 'album', text: '相册', icon: '/static/uploadall/pic.svg' },
  { type: 'file', text: '本地文件', icon: '/static/uploadall/folder.svg' },
  { type: 'school', text: '学校空间', icon: '/static/uploadall/wechat.svg' },
  { type: 'my', text: '我的空间', icon: '/static/uploadall/wechat.svg' },
]);

const internalUploadList = ref([]);
const confirmTrigger = ref(false);
const fileShow = ref(false);
const xeUploadRef = ref(null);
const uploadOptions = ref({});
// 用于传递给LkDatabasePopup的已选文件列表
const selectedFileIds = computed(() => {
  return internalUploadList.value.map(item => item.id).filter(id => id);
});

watch(
  () => props.modelValue,
  newValue => {
    if (JSON.stringify(internalUploadList.value) !== JSON.stringify(newValue)) {
      internalUploadList.value = [...newValue];
    }
  },
  { immediate: true, deep: true }
);

function fileClose() {
  fileShow.value = false;
}

function fileSubmit() {
  fileShow.value = false;
  console.log('internalUploadList.value for submit', internalUploadList.value);
  emit('submit', internalUploadList.value);
}

function uploadToServer(event, type) {
  let files = event;
  uni.showLoading({
    title: '上传中...',
  });

  files.forEach((fileItem, index) => {
    const filePath = type === 'image_url' ? fileItem : fileItem.tempFilePath;

    const filenameQueryParam =
      typeof fileItem.fileType !== 'undefined' && fileItem.fileType === 'file' && fileItem.name
        ? `?filename=${fileItem.name}`
        : '';
    const baseUrl = getBaseUrl();
    uni.uploadFile({
      url: `${baseUrl}/huayun-ai/system/file/public/upload${filenameQueryParam}`,
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      filePath: filePath,
      name: 'file',
      success: uploadResult => {
        let data;
        try {
          data = JSON.parse(uploadResult.data);
        } catch (e) {
          console.error('解析服务器响应失败:', uploadResult.data, e);
          if (index === files.length - 1) {
            uni.hideLoading();
          }
          uni.showToast({ title: '服务器响应格式错误', icon: 'none' });
          return;
        }

        if (data.code == 200) {
          console.log('上传成功:', data);
          const fileData = {
            fileUrl: data.data.fileUrl,
            fileName: data.data.fileName,
            fileKey: data.data.fileKey,
            id: data.data.id,
            fileType: data.data.fileType,
            fileSize: data.data.fileSize,
            type: type,
            uploadTime: Date.now(),
          };
          internalUploadList.value.push(fileData);
          console.log('internalUploadList.value for update:', internalUploadList.value);
          emit('update:modelValue', [...internalUploadList.value]);

          if (!confirmTrigger.value) {
            fileClose();
          } else {
            fileSubmit();
          }
        } else {
          console.error('上传失败:', data);
          uni.showToast({ title: data.message || `上传失败 (${data.code})`, icon: 'none' });
        }

        if (index === files.length - 1) {
          uni.hideLoading();
        }
      },
      fail: err => {
        console.error('上传请求失败:', err);
        uni.hideLoading();
        uni.showToast({ title: '上传请求失败', icon: 'none' });
      },
    });
  });
}

function handleUploadCallback(e) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);
    uploadToServer(e.data, 'file');
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    uni.showToast({ title: e.data, icon: 'none' });
  }
}

function handleConfirmSelectedItems(items) {
  console.log('从数据空间选中的文件:', items);

  // 直接覆盖 internalUploadList，实现同步删除
  internalUploadList.value = [...items];
  emit('update:modelValue', [...internalUploadList.value]);

  // 如果启用了确认触发器，提交表单
  if (confirmTrigger.value) {
    fileSubmit();
  }
}

async function handleItemClick(item) {
  if (item.type === 'album') {
    const hasPermission = await checkPermission('album');
    if (hasPermission) {
      uni.chooseImage({
        count: 9, // 默认9
        sizeType: ['original', 'compressed'],
        sourceType: ['album'], //从相册选择
        success: function (res) {
          const tempFilePaths = res.tempFilePaths;
          uploadToServer(tempFilePaths, 'image_url');
        },
      });
    } else {
      console.log('相册权限未授予');
    }
  } else if (item.type === 'camera') {
    const hasPermission = await checkPermission('camera');
    if (hasPermission) {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: res => {
          const tempFilePaths = res.tempFilePaths;
          uploadToServer(tempFilePaths, 'image_url');
        },
      });
    } else {
      console.log('相机权限未授予');
    }
  } else if (item.type === 'file') {
    xeUploadRef.value.upload('file', {});
  } else if (item.type === 'school') {
    console.log('学校空间');
    bizType.value = '1';
    lkDatabasePopupRef.value?.openPopup();
  } else if (item.type === 'my') {
    console.log('我的空间');
    bizType.value = '2';
    lkDatabasePopupRef.value?.openPopup();
  }
}
</script>

<template>
  <view class="upload-all-container">
    <view v-for="item in items" :key="item.type" class="upload-item" @tap="handleItemClick(item)">
      <view class="icon-bg">
        <LkSvg :src="item.icon" width="56rpx" height="56rpx" />
      </view>
      <text class="upload-text">{{ item.text }}</text>
    </view>
  </view>
  <!-- xe-upload组件 -->
  <xe-upload
    ref="xeUploadRef"
    :options="uploadOptions"
    @callback="handleUploadCallback"
  ></xe-upload>
  <LkDatabasePopup
    ref="lkDatabasePopupRef"
    :bizType="bizType"
    :layoutType="3"
    :preSelectedIds="selectedFileIds"
    @confirmSelectedItems="handleConfirmSelectedItems"
  />
</template>

<style scoped>
.upload-all-container {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding: 32rpx 16rpx;
  width: 100%;
  box-sizing: border-box;
}
.upload-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: calc(20% - 10rpx);
  max-width: 140rpx;
}
.icon-bg {
  width: 100rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  box-shadow: 0px 4rpx 12rpx rgba(0, 0, 0, 0.06);
}
.icon-text-placeholder {
  font-size: 40rpx;
  color: #b8bdc8;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.icon-text-placeholder image {
  opacity: 0.7;
}
.upload-text {
  font-size: 26rpx;
  color: #303133;
  line-height: 1.4;
}
</style>
