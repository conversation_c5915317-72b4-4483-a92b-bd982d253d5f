<template>
  <view class="header-nav" :style="{ paddingTop: `${statusBarHeight}px` }">
    <view class="header-nav__content">
      <view class="header-nav__left" @click="handleBack">
        <u-icon name="arrow-left" size="20" color="#333333"></u-icon>
      </view>
      <view class="header-nav__title">
        <view class="header-nav__title-icon" v-if="titleIcon">
          <image :src="titleIcon" mode="aspectFit" class="title-icon-image"></image>
        </view>
        <text class="header-nav__title-text">{{ title }}</text>
      </view>
      <view class="header-nav__right">
        <!-- <view class="header-nav__right-item" v-if="showRefresh" @click="handleRefresh">
          <LkSvg width="22rpx" height="22rpx" src="/static/chat/comment.svg" />
        </view> -->
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LkSvg from '../svg/index.vue';

interface Props {
  /** 标题文字 */
  title: string;
  /** 标题图标 */
  titleIcon?: string;
  /** 是否显示刷新按钮 */
  showRefresh?: boolean;
  /** 是否显示更多按钮 */
  showMore?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleIcon: '',
  showRefresh: false,
  showMore: false,
});

const emit = defineEmits(['back', 'refresh', 'more']);

// 系统信息
const statusBarHeight = ref(0);

onMounted(() => {
  // 获取系统状态栏高度
  uni.getSystemInfo({
    success: res => {
      statusBarHeight.value = res.statusBarHeight || 0;
    },
  });
});

// 处理返回
const handleBack = () => {
  emit('back');
  uni.navigateBack({
    fail: () => {
      uni.switchTab({
        url: '/pages/index/index',
      });
    },
  });
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};

// 处理更多
const handleMore = () => {
  emit('more');
};
</script>

<style lang="scss">
.header-nav {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  z-index: 999;

  &__content {
    position: relative;
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }

  &__left {
    position: absolute;
    left: 15px;
    display: flex;
    align-items: center;
    height: 100%;
  }

  &__title {
    display: flex;
    align-items: center;
    margin-left: 42px;
    justify-content: flex-start;
    height: 100%;

    &-icon {
      margin-right: 5px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;

      .title-icon-image {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        overflow: hidden;
      }
    }

    &-text {
      font-size: 17px;
      font-weight: 500;
      color: #333333;
      text-align: left;
    }
  }

  &__right {
    position: absolute;
    right: 15px;
    display: flex;
    align-items: center;
    height: 100%;

    &-item {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
