<script setup lang="ts">
import { ref, defineExpose } from 'vue';
type ToastType = 'success' | 'info';
interface ToastOptions {
  message: string;
  type?: ToastType;
  duration?: number;
  icon?: string;
}

const visible = ref(false);
const message = ref('');
const type = ref<ToastType>('info');
const timer = ref<number | null>(null);

// 图标映射
const iconMap = {
  success: 'success',
  // error: 'close-circle',
  // warning: 'warning',
  info: 'info',
};

// 颜色映射
const colorMap = {
  success: '#009968',
  error: '#f53f3f',
  warning: '#ff976a',
  info: '#7d4dff',
};

const show = (options: ToastOptions) => {
  // 清除之前的定时器
  if (timer.value) {
    clearTimeout(timer.value);
  }

  // 设置内容
  message.value = options.message;
  type.value = options.type || 'info';
  visible.value = true;

  // 设置定时关闭
  timer.value = setTimeout(() => {
    visible.value = false;
  }, options.duration || 2000) as unknown as number;
};

const hide = () => {
  visible.value = false;
  if (timer.value) {
    clearTimeout(timer.value);
  }
};

// 暴露方法
defineExpose({
  show,
  hide,
});
</script>

<template>
  <view class="lk-toast" :class="[`lk-toast--${type}`, { 'lk-toast--show': visible }]">
    <image
      :src="`/static/tips/${type}.svg`"
      mode="aspectFit"
      style="width: 40rpx; height: 40rpx"
    ></image>
    <text class="lk-toast__text">{{ message }}</text>
  </view>
</template>

<style lang="scss" scoped>
.lk-toast {
  position: fixed;
  left: 50%;
  top: 175rpx;
  transform: translateX(-50%) scale(0.8);
  opacity: 0;
  padding: 36rpx 28rpx;
  border-radius: 28rpx;
  background-color: white;
  box-shadow:
    0 -2rpx 32rpx 0 rgba(0, 0, 0, 0.14),
    0 2rpx 20rpx 0 rgba(0, 0, 0, 0.05),
    0 4rpx 14rpx -2rpx rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  z-index: 10002;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
  max-width: 80%;

  &--show {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }

  &__text {
    font-size: 28rpx;
    color: #000;
    line-height: 1.5;
    white-space: nowrap;
  }

  :deep(.u-icon__icon) {
    font-size: 40rpx !important;
  }
}
</style>
