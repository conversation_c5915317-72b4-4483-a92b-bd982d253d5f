<template>
  <view class="lk-tenant-switch">
    <!-- 当前租户信息 -->
    <view class="current-tenant" @click="handleShowPopup">
      <!-- <image
        class="tenant-avatar"
        :src="currentTenant?.tenantAvatar || defaultAvatar"
        @error="handleAvatarError"
      /> -->
      <view class="tenant-info">
        <LkText :lines="1" class="tenant-name" ellipsis bold size="large">{{
          currentTenant?.tenantName || '选择租户'
        }}</LkText>
        <!-- <LkText type="secondary" size="small" class="tenant-account">{{
          currentTenant?.account
        }}</LkText> -->
      </view>
      <view class="switch-icon" v-if="tenantList.length > 1">
        <uni-icons type="bottom" size="14" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 租户列表弹窗 -->
    <up-popup
      :safeAreaInsetBottom="true"
      :safeAreaInsetTop="true"
      mode="left"
      :show="popupShow"
      @close="handleClosePopup"
      @open="handleShowPopup"
    >
      <view class="tenant-popup">
        <view class="popup-header">
          <LkText size="large" bold style="position: relative; z-index: 1">切换账号</LkText>
          <LkServiceImage
            name="textLine"
            style="position: absolute; bottom: 0; left: 0; height: 32rpx; width: 120rpx; z-index: 0"
          />
        </view>
        <scroll-view scroll-y class="tenant-list" v-if="tenantList.length">
          <view
            v-for="tenant in tenantList"
            :key="tenant.tenantId"
            class="tenant-item"
            :class="{ 'tenant-item--active': tenant.tenantId === currentTenant?.tenantId }"
            @click="handleSelectTenant(tenant)"
          >
            <!-- <image
              class="tenant-avatar"
              :src="tenant.tenantAvatar || defaultAvatar"
              @error="handleAvatarError"
            /> -->
            <view class="tenant-info">
              <LkText class="tenant-name">{{ tenant.tenantName }}</LkText>
              <LkText type="secondary" size="small" class="tenant-account">{{
                tenant.account
              }}</LkText>
            </view>
          </view>
        </scroll-view>
        <view v-else style="width: 400rpx; margin-top: 30rpx">
          <up-empty mode="data" text="暂无数据" />
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useUserStore } from '@/store/userStore';
import { serviceImageData } from '@/components/LkServiceImage/data';

const userStore = useUserStore();
const popupShow = ref(false);
const defaultAvatar = serviceImageData.defaultAvatar;

// 当前租户信息
const currentTenant = computed(() => userStore.getCurrentTenant);
// 租户列表
const tenantList = computed(() => userStore.getTenantList);

// 显示弹窗
const handleShowPopup = () => {
  if (tenantList.value.length > 1) {
    popupShow.value = true;
  }
};

// 关闭弹窗
const handleClosePopup = () => {
  popupShow.value = false;
};

// 选择租户
const handleSelectTenant = async (tenant: any) => {
  if (tenant.tenantId === currentTenant.value?.tenantId) {
    handleClosePopup();
    emit('switch', tenant);
    return;
  }

  const success = await userStore.switchTenant(tenant.tenantId);
  if (success) {
    handleClosePopup();
    // 发出切换事件
    emit('switch', tenant);
  }
};

// 头像加载失败处理
const handleAvatarError = (e: any) => {
  e.target.src = defaultAvatar;
};

// 定义事件
const emit = defineEmits<{
  (e: 'switch', tenant: any): void;
}>();
</script>

<style lang="scss">
.lk-tenant-switch {
  .current-tenant {
    display: flex;
    align-items: center;

    .tenant-avatar {
      width: 48rpx;
      height: 48rpx;
      border-radius: 24rpx;
      margin-right: 12rpx;
    }

    .tenant-info {
      flex: 1;
      margin-right: 8rpx;

      .tenant-name {
        font-size: 40rpx;
      }

      .tenant-account {
        font-size: 24rpx;
        line-height: 1.2;
      }
    }

    .switch-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
    }
  }

  .tenant-popup {
    padding: 30rpx;
    .popup-header {
      width: 100%;
      box-sizing: border-box;
      position: relative;
      height: 60rpx;
      display: flex;
      align-items: center;
    }

    .tenant-list {
      max-height: calc(100vh - 30rpx * 2 - 60rpx);
      min-height: calc(100vh - 30rpx * 2 - 60rpx);
      .tenant-item {
        display: flex;
        align-items: center;
        padding: 20rpx 30rpx;
        background-color: #f7f8fa;
        border-radius: 28rpx;
        margin-top: 20rpx;
        &--active {
          border: 1px solid #ecc1ff;
          background: linear-gradient(103deg, #f1f8ff 6.83%, #f3efff 90.91%);
        }

        .tenant-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          margin-right: 24rpx;
        }

        .tenant-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 20rpx;
          width: 400rpx;

          .tenant-name {
            font-size: 32rpx;
            line-height: 1.4;
            margin-bottom: 4rpx;
          }

          .tenant-account {
            font-size: 28rpx;
            line-height: 1.2;
          }
        }
      }
    }
  }
}
</style>
