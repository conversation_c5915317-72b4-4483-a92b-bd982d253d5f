<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import { getSpaceFileList, getMySpaceFileList, getRecycleBinFileList } from '@/api/database';
import { ref, defineExpose, nextTick, watch } from 'vue';
import FileViewer from '@/components/LkFileViewer/index.vue';
import LkPageList from '@/components/LkPageList/index.vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/userStore';

type NavOptions = {
  id: string;
  navTitle: string;
  bizType: string;
  itemBizType: number;
};

interface DatabaseItem {
  fileName: string;
  fileType: string;
  fileSize: string;
  fileDate: string;
  creator: string;
  spaceName: string;
  records: any[];
}

const props = defineProps({
  isCreator: {
    type: Boolean,
    default: false,
  },
  isFileInfo: {
    type: Boolean,
    default: false,
  },
  parentId: {
    type: String,
    default: '0',
  },
  isPopup: {
    type: Boolean,
    default: false,
  },
  pathList: {
    type: Array as () => any[],
    default: () => [],
  },
  // 1数据空间 3对话页面
  layoutType: {
    type: Number,
    required: true,
  },
  bizType: {
    type: String,
    required: true,
  },
  optionsList: {
    type: Array as () => any[],
    // 初始值是[{}]不能是[],否则disabled不生效
    default: () => [{}],
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
  preSelectedIds: {
    type: Array as () => string[],
    default: () => [],
  },
});

const urlOptions = ref<NavOptions>();
const userStore = useUserStore();
const currentParentId = ref(props.parentId || '0');
const fileProps = ref({
  fileUrl: '',
  fileType: '',
});
const lkPageListRef = ref<InstanceType<typeof LkPageList>>();
const searchKeywordRef = ref('');

onLoad(options => {
  urlOptions.value = { ...options } as NavOptions;
});

const checkboxValue = ref<string[]>([]);
const currentList = ref<any[]>([]);

const changeCheckbox = (value: string[], item: any) => {
  console.log('changeCheckbox', item);
  // 如果是layoutType === 3，则处理复选框逻辑
  if (props.layoutType === 3) {
    toggleItemSelection(item);
  }

  // // 根据checkbox的值更新selectedItems
  // selectedItems.value = currentList.value.filter(item => value.includes(item.id));

  // 通知父组件选中项变化
  emit('updateSelectedItems', [...selectedItems.value]);
};

const selectedItems = ref<any[]>([]);
const emit = defineEmits(['updatePathData', 'updateSelectedItems', 'clickOption']);
// 使用 LkPageList 替代原有的加载逻辑
const fetchSpaceFileList = async (params: any) => {
  const apiParams = {
    ...params,
    privilege: userStore.curPrivilege,
    searchKey: searchKeywordRef.value,
  };
  console.log('params with keyword:', apiParams);

  if (props.bizType === '1' || urlOptions.value?.itemBizType == 1) {
    return await getSpaceFileList(apiParams);
  } else if (props.bizType === '2' || urlOptions.value?.itemBizType == 2) {
    return await getMySpaceFileList(apiParams);
  } else if (props.bizType === '3' && !urlOptions.value?.itemBizType) {
    return await getRecycleBinFileList(apiParams);
  }
};

const processSpaceFileData = (data: any) => {
  console.log(data);
  return data;
};

const clickDatabaseItem = (item: any) => {
  console.log('item', item);
  const maxPrivilege = item.privileges?.length ? Math.max(...item.privileges) : 4;
  userStore.curPrivilege = maxPrivilege || 4;
  if (item.fileType === 3 || item.fileType === 2) {
    if (props.isPopup) {
      // 更新路径数据
      emit('updatePathData', item);
      // 更新空间数据
      currentParentId.value = item.id;
    } else {
      let navTitle = '';
      if (props.bizType === '1') {
        navTitle = item.spaceName;
      } else if (props.bizType === '2') {
        navTitle = item.folderName;
      } else if (props.bizType === '3') {
        navTitle = item.fileName;
      }
      // 回收站的情况下用itemBizType来请求对应空间的数据,bizType用来处理前端交互逻辑
      if (props.bizType === '3') {
        // 跳转子空间页面
        uni.navigateTo({
          url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.bizId}&navTitle=${navTitle}&bizType=${props.bizType}&itemBizType=${item.bizType}`,
        });
      } else {
        // 跳转子空间页面
        uni.navigateTo({
          url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.id}&navTitle=${navTitle}&bizType=${props.bizType}`,
        });
      }
    }
  } else if (item.fileType === 1) {
    // 如果类型符合,则打开文件
    console.log('打开文件');
    fileProps.value = {
      fileUrl: item.file?.fileUrl,
      fileType: item.fileType,
    };
    console.log(fileProps);
  }
};

// 切换项目选中状态
const toggleItemSelection = (item: any) => {
  console.log('checkboxValue', checkboxValue.value);
  // 检查项目是否已经选中
  const index = selectedItems.value.findIndex(i => i.id === item.id);
  if (index > -1) {
    // 如果已选中，则移除
    selectedItems.value.splice(index, 1);
    // 同时更新checkbox值
    const checkboxIndex = checkboxValue.value.indexOf(item.id);
    if (checkboxIndex > -1) {
      checkboxValue.value.splice(checkboxIndex, 1);
    }
  } else {
    // 如果未选中，则添加
    selectedItems.value.push(item);
    // 同时更新checkbox值
    if (!checkboxValue.value.includes(item.id)) {
      checkboxValue.value.push(item.id);
    }
  }

  emit('updateSelectedItems', [...selectedItems.value]);
};

// 检查项目是否被选中
const isItemSelected = (item: any) => {
  return selectedItems.value.some(i => i.id === item.id);
};

const fileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const formatFileSize = (bytes: number, withoutB?: boolean): string => {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + (withoutB ? sizes[i][0] : sizes[i]);
};

const setSearchKeywordAndRefresh = (keyword: string) => {
  searchKeywordRef.value = keyword;
  nextTick(() => {
    if (lkPageListRef.value) {
      lkPageListRef.value.refresh();
    }
  });
};

function syncList(listFromSlot: any[]) {
  currentList.value = listFromSlot;
}

async function toggleCheckAll() {
  if (checkboxValue.value.length === currentList.value.length) {
    checkboxValue.value = [];
  } else {
    checkboxValue.value = currentList.value.map(item => item.id);
  }
  await nextTick();
  console.log(checkboxValue.value);
}

// 设置预选中的文件IDs
const setPreSelectedIds = (ids: string[]) => {
  if (!ids || ids.length === 0) return;

  console.log('设置预选ID:', ids);

  // 清空当前选择
  selectedItems.value = [];
  checkboxValue.value = [];

  // 获取当前列表中与预选ID匹配的项
  const matchedItems = currentList.value.filter(item => ids.includes(item.id));

  if (matchedItems.length > 0) {
    // 更新选中状态
    selectedItems.value = [...matchedItems];
    checkboxValue.value = matchedItems.map(item => item.id);

    // 通知父组件
    emit('updateSelectedItems', [...selectedItems.value]);
  }

  console.log('预选匹配项:', matchedItems);
};

// 当数据列表加载完成后，应用预选ID
watch(
  () => currentList.value,
  newList => {
    if (newList.length > 0 && props.preSelectedIds && props.preSelectedIds.length > 0) {
      setPreSelectedIds(props.preSelectedIds);
    }
  }
);

// 初始化时也检查预选ID
watch(
  () => props.preSelectedIds,
  (newIds: string[]) => {
    // 1. 更新checkboxValue
    checkboxValue.value = [...(newIds as string[])];
    // 2. 更新selectedItems
    selectedItems.value = currentList.value.filter(item => (newIds as string[]).includes(item.id));
  },
  { immediate: true }
);

defineExpose({
  currentParentId,
  lkPageListRef,
  setSearchKeywordAndRefresh,
  setPreSelectedIds,
});
</script>

<template>
  <view class="lk-database-main" :style="{ height: isPopup ? '100%' : 'calc(100vh - 44px)' }">
    <u-swipe-action>
      <LkPageList
        :fetch-method="fetchSpaceFileList"
        :extra-params="{ parentId: currentParentId }"
        :process-data="processSpaceFileData"
        :page-size="10"
      >
        <template #default="{ list }">
          <component
            :is="
              () => {
                syncList(list);
                return null;
              }
            "
          />
          <up-checkbox-group v-model="checkboxValue" placement="column">
            <up-swipe-action-item
              v-for="item in list"
              :key="item.id"
              :options="optionsList"
              @click="
                (obj: any) => {
                  console.log('u-swipe-action-item click obj in LkDatabaseItem:', obj);
                  emit('clickOption', { eventData: obj, itemData: item });
                }
              "
              :disabled="isPopup || layoutType === 3"
            >
              <view class="lk-database-item" @click="clickDatabaseItem(item)">
                <view class="wrapContent">
                  <!-- 复选框 -->
                  <up-checkbox
                    v-if="isEditing"
                    :name="item.id"
                    activeColor="#6d51f6"
                    :customStyle="{}"
                    shape="circle"
                    @change="changeCheckbox($event, item)"
                  >
                  </up-checkbox>
                  <view class="content">
                    <LkSvg
                      @click.stop="toggleCheckAll"
                      class="fileIcon"
                      width="84rpx"
                      height="84rpx"
                      :src="fileIcon(item)"
                      :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                    />
                    <view class="wrapTxt">
                      <view class="fileName" v-if="bizType === '3'">
                        {{ item.fileName }}
                      </view>
                      <view class="fileName" v-else>{{
                        item.fileType === 3 ? item.spaceName : item.fileName
                      }}</view>
                      <!-- 数据空间 -->
                      <view class="fileInfo" v-if="layoutType === 1">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                        <text class="fileCreator" v-if="bizType === '1'">{{ item.uploader }}</text>
                      </view>
                      <!-- popup -->
                      <view class="fileInfo" v-if="layoutType === 2">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                      </view>
                      <!-- 对话页面 -->
                      <view class="fileInfo" v-if="layoutType === 3">
                        <text class="fileSize" v-if="item.fileType === 1">{{
                          formatFileSize(item.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.fileType === 1"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                        <text class="fileCreator" v-if="bizType === '1'">{{ item.uploader }}</text>
                      </view>
                      <!-- 回收站 -->
                      <view class="fileInfo" v-if="layoutType === 4">
                        <text class="fileSize" v-if="item.file">{{
                          formatFileSize(item.file.fileSize)
                        }}</text>
                        <view class="sign" v-if="item.file"></view>
                        <text class="fileDate">{{ item.updateTime }}</text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="bottom" v-if="layoutType === 4">
                  <LkSvg width="36rpx" height="36rpx" src="/static/recycleBin/pathBg.svg" />
                  <view class="path">{{
                    item.location || (item.bizType === 1 ? '学校数据空间' : '我的数据空间')
                  }}</view>
                  <view class="delUser">
                    {{ item.deleterName }}
                  </view>
                </view>
              </view>
            </up-swipe-action-item>
          </up-checkbox-group>
        </template>
      </LkPageList>
    </u-swipe-action>
    <FileViewer :file="fileProps" @resetFile="fileProps = { fileUrl: '', fileType: '' }" />
  </view>
</template>
<style lang="scss" scoped>
.lk-database-main {
  .u-swipe-action-item {
    margin-top: 12px;
  }
  .u-swipe-action {
    height: 100%;
  }
  ::v-deep .u-swipe-action-item__content {
    display: flex;
    align-items: center;
    height: 100%;
  }
  ::v-deep .u-swipe-action-item__right {
    .u-swipe-action-item__right__button {
      &:last-child {
        border-radius: 0 14px 14px 0;
      }
    }
  }
  .lk-database-item {
    display: flex;
    flex-direction: column;
    border-radius: 14px;
    border: 1px solid #f4f4f4;
    background: #fff;
    box-shadow: 0px 0px 2px 0px rgba(237, 237, 237, 0.62);
    padding: 14.39px 16px 13.61px 16px;
    overflow: hidden;
    flex: 1;
    > .wrapContent {
      display: flex;
      align-items: center;
      ::v-deep .u-checkbox__icon-wrap {
        flex-shrink: 0;
        margin-right: 12px;
        width: 21px !important;
        height: 21px !important;
      }
      > .content {
        display: flex;
        align-items: center;
        width: 100%;
        .fileIcon {
          flex-shrink: 0;
          margin-right: 10px;
        }
        .wrapTxt {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .fileName {
            color: #1d2129;
            font-family: 'PingFang SC';
            font-size: 32rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
          }
          .fileInfo {
            display: flex;
            align-items: center;
            margin-top: 3px;
            .fileSize {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .sign {
              width: 1px;
              height: 9px;
              background: #86909c;
              margin: 0 6px;
            }
            .fileDate {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .fileCreator {
              margin-left: auto;
              margin-right: 16px;
              font-size: 24rpx;
              color: #4e5969;
              letter-spacing: 0.06px;
            }
          }
        }
      }
    }
    > .bottom {
      width: 100%;
      margin-top: 12px;
      padding-top: 10px;
      border-top: 1px solid #f3f3f3;
      display: flex;
      align-items: center;
      .path {
        margin-left: 4px;
        color: #86909c;
        font-size: 24rpx;
      }
      .delUser {
        margin-left: auto;
        color: #86909c;
        font-size: 24rpx;
      }
    }
  }
}
</style>
