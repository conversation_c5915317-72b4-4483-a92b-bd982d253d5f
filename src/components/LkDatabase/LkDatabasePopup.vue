<script setup lang="ts">
import { ref, onMounted, defineEmits, watch } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import LkDatabaseItem from '@/components/LkDatabase/LkDatabaseItem.vue';
import LkDatabasePath from '@/components/LkDatabase/LkDatabasePath.vue';
import LkSearch from '@/components/LkSearch/index.vue';
const props = defineProps<{
  bizType: string;
  layoutType: number;
  preSelectedIds?: string[];
}>();

const emit = defineEmits(['confirmRecovery', 'confirmSelectedItems']);

const isPopup = ref(false);
const searchValue = ref('');

// 监听预选ID变化
watch(
  () => props.preSelectedIds,
  newIds => {
    if (isPopup.value && newIds && newIds.length > 0 && databaseItemRef.value) {
      // 设置预选ID到数据库项组件
      databaseItemRef.value.setPreSelectedIds(newIds);
    }
  }
);

const openPopup = () => {
  isPopup.value = true;
  // 重置选中状态，但保留已预选的项
  if (props.preSelectedIds && props.preSelectedIds.length > 0) {
    // 延迟设置预选项，确保组件已经挂载
    setTimeout(() => {
      if (databaseItemRef.value) {
        databaseItemRef.value.setPreSelectedIds(props.preSelectedIds);
      }
    }, 100);
  } else {
    selectedItems.value = [];
  }
};

const closePopup = () => {
  isPopup.value = false;
  // 清空路径数据
  pathList.value = [];
  // 不清空选中项，保留当前选择状态
};

defineExpose({
  openPopup,
});

const pathList = ref<any[]>([]);
const databaseItemRef = ref<any>(null);
const selectedItems = ref<any[]>([]);

const updatePathData = (pathData: any) => {
  console.log(pathData);
  // pathList.value.push({
  //   spaceName: pathData?.spaceName,
  //   id: pathData?.id,
  // });
  pathList.value.push(pathData);
};
const updateSpaceData = (spaceData: any) => {
  console.log(spaceData);
  console.log(databaseItemRef.value.currentParentId);
  databaseItemRef.value.currentParentId = spaceData?.id || '0';
};
const clickSave = () => {
  console.log(databaseItemRef.value.currentParentId);
  emit('confirmRecovery', databaseItemRef.value.currentParentId);
};

// 更新选中的项
const updateSelectedItems = (items: any[]) => {
  selectedItems.value = items;
  console.log('已选中的项:', items);
};

// 搜索处理函数
const handleSearch = (value: string) => {
  console.log('搜索关键词:', value);
  if (databaseItemRef.value) {
    databaseItemRef.value.setSearchKeywordAndRefresh(value);
  }
};

// 清除搜索
const handleClear = () => {
  console.log('清除搜索');
  searchValue.value = '';
  if (databaseItemRef.value) {
    databaseItemRef.value.setSearchKeywordAndRefresh('');
  }
};

const handleSave = () => {
  console.log('选中的项ID:', selectedItems.value);

  if (selectedItems.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个文件',
      icon: 'none',
    });
    return;
  }

  // 处理选中的文件，转换为LKUploadall需要的格式
  const processedItems = selectedItems.value.map(item => {
    return {
      fileUrl: item.file?.fileUrl || item.fileUrl,
      fileName: item.fileName,
      fileKey: item.file?.fileKey || item.fileKey,
      id: item.id,
      fileType: item.fileType,
      fileSize: item.fileSize || item.file?.fileSize,
      type: 'file',
      uploadTime: Date.now(),
    };
  });

  console.log('processedItems', processedItems);
  emit('confirmSelectedItems', processedItems);
  closePopup();
};
</script>
<template>
  <view class="lk-database">
    <up-popup :show="isPopup" mode="bottom" @close="closePopup" :safeAreaInsetBottom="true">
      <view class="wrapMain">
        <!-- header -->
        <view class="lk-database-header">
          <text class="title">{{ bizType === '1' ? '学校数据空间' : '我的数据空间' }}</text>
          <LkSvg
            class="close-btn"
            width="24px"
            height="24px"
            src="/static/database/close.svg"
            @click="closePopup"
          />
        </view>
        <!-- search -->
        <view v-if="layoutType === 3" class="lk-database-search">
          <LkSearch
            v-model="searchValue"
            placeholder="请输入关键词搜索"
            @search="handleSearch"
            @clear="handleClear"
          />
        </view>
        <!-- main -->
        <view class="lk-database-main">
          <!-- path -->
          <LkDatabasePath
            v-model="pathList"
            :bizType="bizType"
            @updateSpaceData="updateSpaceData"
          />
          <scroll-view scroll-y class="lk-database-main-scroll">
            <!-- item -->
            <LkDatabaseItem
              ref="databaseItemRef"
              :layoutType="layoutType"
              :isPopup="true"
              :pathList="pathList"
              :bizType="bizType"
              @updatePathData="updatePathData"
              @updateSelectedItems="updateSelectedItems"
              :isEditing="layoutType === 3"
              :preSelectedIds="preSelectedIds"
            />
          </scroll-view>
          <LkButton
            v-if="layoutType === 3"
            type="primary"
            shape="round"
            class="saveBtn"
            @click="handleSave"
            >确认添加</LkButton
          >
          <LkButton v-else type="primary" shape="round" class="saveBtn" @click="clickSave"
            >保存当前位置</LkButton
          >
        </view>
      </view>
    </up-popup>
  </view>
</template>
<style lang="scss" scoped>
.lk-database {
  ::v-deep .u-popup__content {
    padding: 16px;
    padding-bottom: 14px;
    border-radius: 12px 12px 0px 0px;
    z-index: 9999;
  }
}
.wrapMain {
  min-height: 60vh;
  max-height: 60vh;
}
.lk-database-header {
  display: flex;
  justify-content: center;
  align-items: center;
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1d2129;
  }
  .close-btn {
    position: absolute;
    right: 14px;
    top: 16px;
  }
}
.lk-database-main {
  display: flex;
  flex-direction: column;
  .lk-database-main-scroll {
    height: 100%;
    width: 100%;
    height: 40vh;
    min-height: 40vh;
    max-height: 40vh;
    margin-bottom: 40px;
  }
  .saveBtn {
    width: 100%;
    margin: 14px 0;
    flex-shrink: 0;
  }
}
.lk-database-search {
  margin: 24rpx 0;
}
</style>
