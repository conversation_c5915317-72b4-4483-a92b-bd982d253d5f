<template>
  <image
    :class="['lk-svg', customClass]"
    :style="style"
    :src="imageData"
    :mode="mode"
    @tap="e => $emit('click', e)"
    @error="handleError"
  >
  </image>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { encode } from 'js-base64';

const props = defineProps({
  customClass: {
    type: String,
    default: '',
  },
  customStyle: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: String,
    default: '',
  },
  height: {
    type: String,
    default: '',
  },
  src: {
    type: String,
    default: '',
  },
  mode: {
    type: String,
    default: 'scaleToFill',
  },
  color: {
    type: String,
    default: '',
  },
  errorSrc: {
    type: String,
    default: '',
  },
});

defineEmits(['click']);
const handleError = () => {
  rawData.value = props.errorSrc;
  rawType.value = 'url';
};
const rawData = ref('');
const rawType = ref(''); // url raw base64

const style = computed(() => {
  const styleObj = {
    ...props.customStyle,
  };
  if (props.width) {
    styleObj.width = props.width;
  }
  if (props.height) {
    styleObj.height = props.height;
  }
  return styleObj;
});

const imageData = computed(() => {
  if (rawType.value === 'url' || rawType.value === 'base64') {
    return rawData.value;
  }

  if (rawType.value === 'raw' && rawData.value) {
    const processedData = props.color
      ? rawData.value.replace(/currentColor/g, props.color)
      : rawData.value;
    const base64Data = encode(processedData);
    return `data:image/svg+xml;base64,${base64Data}`;
  }

  return '';
});

const getHttpData = () => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: props.src,
      success: res => resolve(res.data),
      fail: err => reject(err),
    });
  });
};

const getData = async () => {
  if (!props.src) {
    return Promise.reject('src empty');
  }

  // 处理http/https链接
  if (props.src.startsWith('http')) {
    return await getHttpData();
  }

  // 处理本地文件路径
  // #ifdef APP-PLUS
  // 在APP环境下，读取本地文件
  return new Promise((resolve, reject) => {
    plus.io.resolveLocalFileSystemURL(
      props.src,
      entry => {
        entry.file(
          file => {
            const reader = new plus.io.FileReader();
            reader.onload = e => {
              resolve(e.target.result);
            };
            reader.onerror = e => {
              reject(e);
            };
            reader.readAsText(file);
          },
          error => {
            reject(error);
          }
        );
      },
      error => {
        // 如果不能解析本地文件路径，尝试作为网络路径处理
        getHttpData().then(resolve).catch(reject);
      }
    );
  });
  // #endif

  // #ifdef MP
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath: props.src,
      success: res => resolve(res.data),
      fail: err => reject(err),
      encoding: 'utf-8',
    });
  });
  // #endif

  // 对于其他平台，或者当条件编译没有匹配任何环境时
  // 尝试将src当作远程资源加载
  return await getHttpData();
};

const load = () => {
  const useGetData = !!props.color;

  if (!useGetData) {
    // 不需要处理颜色的情况
    // 检查是否为本地文件路径
    if (props.src && !props.src.startsWith('http') && !props.src.startsWith('data:')) {
      // #ifdef APP-PLUS
      // 在APP中，本地路径需要特殊处理
      rawData.value = plus.io.convertLocalFileSystemURL(props.src);
      // #endif

      // #ifndef APP-PLUS
      rawData.value = props.src;
      // #endif
    } else {
      rawData.value = props.src;
    }
    rawType.value = 'url';
    return;
  }

  getData()
    .then(data => {
      if (typeof data === 'string' && data.includes('currentColor')) {
        rawData.value = data;
        rawType.value = 'raw';
      } else {
        rawData.value = `data:image/svg+xml;base64,${encode(data)}`;
        rawType.value = 'base64';
      }
    })
    .catch(err => {
      console.error('加载SVG出错:', err);
    });
};

watch(
  () => props.src,
  () => {
    load();
  },
  { immediate: true }
);
</script>

<style></style>
