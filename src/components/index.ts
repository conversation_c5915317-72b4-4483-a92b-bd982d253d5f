// 导出所有组件，简化导入
import TabbarLayout from './TabbarLayout.vue';
import SplashAd from './SplashAd.vue';
import LkServiceImage from './LkServiceImage/index.vue';
import LkImage from './LkImage/index.vue';
import LkButton from './LkButton/index.vue';
import LkText from './LkText/index.vue';
import LkSlideCode from './LkSlideCode/index.vue';
import LkNavBar from './LkNavBar/index.vue';
import HeaderNav from './LkNavBar/HeaderNav.vue';
import LkLinkList from './LkLinkList/index.vue';
import LkSelectList from './LkSelectList/index.vue';
import LkSelectPopupList from './LkSelectPopupList/index.vue';
import LkTabGroup from './LkTabGroup/index.vue';
import LkLoading from './LkLoading/index.vue';
import LkPageList from './LkPageList/index.vue';
import LkStepGuide from './LkStepGuide/index.vue';
import LkToast from './LkToast/index.vue';

export {
  TabbarLayout,
  SplashAd,
  LkServiceImage,
  LkImage,
  LkButton,
  LkText,
  LkSlideCode,
  LkNavBar,
  HeaderNav,
  LkLinkList,
  LkSelectList,
  LkSelectPopupList,
  LkTabGroup,
  LkLoading,
  LkPageList,
  LkStepGuide,
  LkToast,
};

// 批量注册组件
export const registerGlobalComponents = (app: any) => {
  app.component('TabbarLayout', TabbarLayout);
  app.component('SplashAd', SplashAd);
  app.component('LkServiceImage', LkServiceImage);
  app.component('LkImage', LkImage);
  app.component('LkButton', LkButton);
  app.component('LkText', LkText);
  app.component('LkSlideCode', LkSlideCode);
  app.component('LkNavBar', LkNavBar);
  app.component('HeaderNav', HeaderNav);
  app.component('LkLinkList', LkLinkList);
  app.component('LkSelectList', LkSelectList);
  app.component('LkSelectPopupList', LkSelectPopupList);
  app.component('LkTabGroup', LkTabGroup);
  app.component('LkLoading', LkLoading);
  app.component('LkPageList', LkPageList);
  app.component('LkStepGuide', LkStepGuide);
  app.component('LkToast', LkToast);
};
