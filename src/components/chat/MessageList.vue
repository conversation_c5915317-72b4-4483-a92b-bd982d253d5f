<template>
  <view class="message-list" :style="customStyle">
    <!-- 欢迎消息 -->
    <view
      v-if="welcomeText"
      class="welcome-message"
      :class="{ 'centered-welcome': !hasMessages, 'top-welcome': hasMessages }"
    >
      <view class="app-avatar" v-if="appAvatarUrl && !hasMessages">
        <image :src="appAvatarUrl" class="avatar-image" mode="aspectFill"></image>
      </view>
      <view class="welcome-content">
        <view class="message-bubble ai-message">
          <template v-for="(item, index) in parsedText" :key="index">
            <ua-markdown v-if="item.type === 'text'" :source="item.content" />
            <text
              v-else-if="item.type === 'link'"
              class="dynamic-link"
              @click="handleLinkClick(item.content)"
              >{{ item.content }}</text
            >
          </template>
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view
      v-for="(item, index) in sortedList"
      :key="item.dataId || index"
      class="message-item"
      :class="[item.obj === ChatRoleEnum.human ? 'user-message-item' : 'ai-message-item']"
    >
      <!-- 用户消息 -->
      <template v-if="item.obj === ChatRoleEnum.human">
        <view class="user-message-wrapper">
          <!-- 文本消息 -->
          <view
            v-if="getMessageContent(item)"
            class="message-content user-message-content"
          >
            <view
              class="message-bubble user-message"
              @longpress="showMessageActions(item, $event)"
            >
              {{ getMessageContent(item) }}
            </view>
          </view>

          <!-- 文件列表 - 独立容器 -->
          <view v-if="hasFiles(item)" class="user-file-list-wrapper">
            <view class="user-file-list">
              <view
                v-for="(file, fileIndex) in getDisplayFiles(item)"
                :key="fileIndex"
                class="user-file-item"
                @tap="openFile(file)"
              >
                <view v-if="file.type === 'image'" class="user-file-image">
                  <image :src="file.url" class="image-preview" mode="aspectFill"></image>
                </view>
                <view v-else class="user-file-document">
                  <view class="user-file-card">
                    <view class="file-icon-placeholder">
                      <LkSvg
                        class="fileIcon"
                        width="42px"
                        height="42px"
                        :src="fileIcon(item)"
                        :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                      />
                    </view>
                    <view class="file-details-container">
                      <view class="file-name">
                        {{ file.name }}
                      </view>
                      <view class="file-info">
                        {{ file.name.split('.').pop() }}, {{ formatFileSize(file.size) }}
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 展开/收起按钮 -->
              <view
                v-if="getFiles(item).length > 3"
                class="user-expand-toggle"
                @tap.stop="toggleFileListExpand(item.dataId || '')"
              >
                <text>{{ expandedFileListsMap[item.dataId || ''] ? '收起' : '展开' }}</text>
                <LkSvg
                  v-if="expandedFileListsMap[item.dataId || '']"
                  width="24rpx"
                  height="24rpx"
                  src="/static/chat/chevron-down.svg"
                />
                <LkSvg v-else width="24rpx" height="24rpx" src="/static/chat/chevron-up.svg" />
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- AI 消息 -->
      <template v-else>
        <view class="message-content ai-message-content">
          <!-- 加载状态 -->

          <!-- 消息内容 -->
          <view class="message-bubble ai-message">
            <view v-if="item.status != 'finish'" class="loading-indicator">
              <c-lottie
                ref="cLottieRef"
                :width="`${(130 / 3) * 2}rpx`"
                :height="`${(45 / 3) * 2}rpx`"
                :src="soundlottie.chatting"
                :loop="true"
              />
              <view v-if="isParsingBackground"> 解析背景知识中... </view>
              <view v-else-if="isParsingFiles"> 解析文件中... </view>
              <view v-else> 生成中... </view>
            </view>
            <MessageThink
              v-if="getReasoningContent(item)"
              :content="getReasoningContent(item)"
              :isLoading="item.status !== 'finish'"
            />
            <ua-markdown :source="getMessageContent(item)" />
            <view class="message-actions">
              <view class="action-list">
                <view class="action-item">
                  <view v-if="!soundRef">
                    <LkSvg
                      v-if="item.status === 'finish'"
                      width="44rpx"
                      height="44rpx"
                      @tap="handleSound(item)"
                      src="/static/chat/sound.svg"
                    />
                  </view>
                  <view v-else>
                    <c-lottie
                      ref="cLottieRef"
                      :width="`${(130 / 3) * 2}rpx`"
                      :height="`${(45 / 3) * 2}rpx`"
                      :src="soundlottie.waveform"
                      :loop="true"
                    />
                  </view>
                </view>
                <view class="action-item">
                  <LkSvg
                    @tap="handleCopy(item)"
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    src="/static/chat/copy.svg"
                  />
                </view>
                <view class="action-item" @tap="retryMessage(item)">
                  <LkSvg
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    src="/static/chat/refresh.svg"
                  />
                </view>
              </view>

              <view class="action-list">
                <view class="action-item">
                  <LkSvg
                    @tap="updateFeedback(item, 'like')"
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    src="/static/chat/like.svg"
                  />
                </view>
                <view class="action-item" @tap="updateFeedback(item, 'dislike')">
                  <LkSvg
                    v-if="item.status === 'finish'"
                    width="44rpx"
                    height="44rpx"
                    src="/static/chat/unlike.svg"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 文件列表 -->
          <view v-if="hasFiles(item)" class="file-list">
            <view
              v-for="(file, fileIndex) in getDisplayFiles(item)"
              :key="fileIndex"
              class="file-item"
              @tap="openFile(file)"
            >
              <view v-if="file.type === 'image'" class="file-image">
                <image :src="file.url" class="image-preview" mode="aspectFill"></image>
              </view>
              <view v-else class="file-document">
                <view class="file-icon">
                  <text class="document-icon"></text>
                </view>
                <view class="file-name">{{ file.name }}</view>
              </view>
            </view>
            <!-- 展开/收起按钮 -->
            <view
              v-if="getFiles(item).length > 3"
              class="expand-toggle"
              @tap.stop="toggleFileListExpand(item.dataId || '')"
            >
              <text
                >{{ expandedFileListsMap[item.dataId || ''] ? '收起' : '展开' }}
                <LkSvg
                  v-if="expandedFileListsMap[item.dataId || '']"
                  width="24rpx"
                  height="24rpx"
                  src="/static/chat/chevron-down.svg" />
                <LkSvg v-else width="24rpx" height="24rpx" src="/static/chat/chevron-up.svg"
              /></text>
            </view>
          </view>

          <!-- 消息底部操作栏 -->
        </view>
      </template>
    </view>

    <!-- 长按消息弹出层 -->
    <view class="longpress-popup" v-if="showPopup" :style="popupStyle">
      <view class="popup-item" @tap="handleCopyFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/copy_2.svg" />
        <text>复制</text>
      </view>
      <view class="popup-item" @tap="handleEditFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/edit.svg" />
        <text>编辑</text>
      </view>
      <view class="popup-item" @tap="handleEditFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/mic.svg" />
        <text>语音播放</text>
      </view>
      <view class="popup-item" @tap="handleDeleteFromPopup">
        <LkSvg width="44rpx" height="44rpx" src="/static/chat/delete_2.svg" />
        <text>删除</text>
      </view>
    </view>

    <!-- 遮罩层，点击关闭弹出菜单 -->
    <view class="popup-mask" v-if="showPopup" @tap="closePopup"></view>

    <!-- 消息编辑组件 -->
    <MessageEdit
      v-model:visible="showEditModal"
      :content="currentEditContent"
      @confirm="handleEditConfirm"
      @cancel="showEditModal = false"
    />
  </view>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue';
import { respDims } from '@/utils/respDims';
import LkSvg from '../svg/index.vue';
import cLottie from '@/uni_modules/c-lottie/components/c-lottie/c-lottie.vue';
import { startVoice, stopVoice } from '@/common/ai/voice';
import ai from '@/common/ai';
import MessageEdit from './MessageEdit.vue';
import uaMarkdown from '../ua-markdown/ua-markdown.vue';
import MessageThink from './MessageThink.vue';

import { ChatRoleEnum } from '@/common/ai/fetch';

// Props定义
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  customStyle: {
    type: Object,
    default: () => ({}),
  },
  welcomeText: {
    type: String,
    default: '',
  },
  appAvatarUrl: {
    type: String,
    default: '',
  },
  isResponseLoading: {
    type: Boolean,
    default: false,
  },
  isParsingBackground: {
    type: Boolean,
    default: false,
  },
  isParsingFiles: {
    type: Boolean,
    default: false,
  },
  finalAppId: {
    type: String,
    default: '',
  },
});

const cLottieRef = ref();

const soundRef = ref(false);
const getTenantAppIds = () => {
  const tenantAppIds = uni.getStorageSync('selectApp');
  console.log('tenantAppIds', tenantAppIds);
  return tenantAppIds;
};

const soundlottie = {
  chatting: 'https://huayun-ai-obs-public.huayuntiantu.com/f0352dc439fd1f259c5302f80da2f117.json',
  waveform: 'https://huayun-ai-obs-public.huayuntiantu.com/73a4187f53ef7532280b747b37cd7fe0.json',
};

// 跟踪每个消息的文件列表展开状态
const expandedFileListsMap = reactive({});

// 长按弹窗状态
const showPopup = ref(false);
const popupStyle = ref({});
const currentLongPressMessage = ref(null);

const parsedText = ref([]);

watch(
  () => props.welcomeText,
  newText => {
    parsedText.value = parseText(newText);
  },
  { immediate: true }
);

// 编辑消息状态
const showEditModal = ref(false);
const currentEditContent = ref('');

// 事件
const emit = defineEmits(['updateMessage', 'retryMessage', 'updateContent', 'sendMessage']);

// 响应式状态
const activeOptions = ref('');

// 计算属性
const sortedList = computed(() => {
  return [...props.list].sort((a, b) => (a.createTimeValue || 0) - (b.createTimeValue || 0));
});

// 判断是否有消息列表，用于控制欢迎消息的位置
const hasMessages = computed(() => {
  return props.list && props.list.length > 0;
});

// 方法
function toggleOptions(id) {
  activeOptions.value = activeOptions.value === id ? '' : id;
}

function updateFeedback(message, type) {
  emit('updateMessage', {
    dataId: message.dataId,
    isLike: type,
  });
  activeOptions.value = '';
}

const fileIcon = item => {
  console.log('fileIconitem', item);
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

function retryMessage(message) {
  emit('retryMessage', message);
  activeOptions.value = '';
}

function handleLinkClick(link) {
  emit('updateContent', link);
}

function updateContent(content) {
  emit('updateContent', content);
}

function parseText(text) {
  const regex = /\[([^\]]+)\]/g;
  let result;
  let lastIndex = 0;
  const parsed = [];

  while ((result = regex.exec(text)) !== null) {
    if (result.index > lastIndex) {
      parsed.push({
        type: 'text',
        content: text.slice(lastIndex, result.index),
      });
    }
    parsed.push({
      type: 'link',
      content: result[1],
    });
    lastIndex = regex.lastIndex;
  }

  if (lastIndex < text.length) {
    parsed.push({
      type: 'text',
      content: text.slice(lastIndex),
    });
  }

  return parsed;
}

function handleSound(message) {
  soundRef.value = !soundRef.value;
  if (soundRef.value) {
    ai.startTts({
      input: getMessageContent(message),
      chatItemId: message.dataId,
      appId: props.finalAppId,
      onStart: () => {
        console.log('语音播放开始');
      },
      onComplete: () => {
        console.log('语音播放完成');
        soundRef.value = false;
      },
      onError: error => {
        console.log('语音播放错误', error);
        soundRef.value = false;
      },
    });
  } else {
    ai.stopTts();
  }
}

function handleCopy(message) {
  uni.setClipboardData({
    data: getMessageContent(message),
  });
}

// 长按消息显示操作浮框
function showMessageActions(message, event) {
  currentLongPressMessage.value = message;

  // 计算弹窗位置
  const touchX = event.touches[0].clientX;
  const touchY = event.touches[0].clientY;

  // 设置弹窗样式和位置
  popupStyle.value = {
    left: touchX - 120 + 'px',
    top: touchY - 100 + 'px',
  };

  showPopup.value = true;
}

// 关闭弹出菜单
function closePopup() {
  showPopup.value = false;
}

// 从弹窗复制消息
function handleCopyFromPopup() {
  if (currentLongPressMessage.value) {
    handleCopy(currentLongPressMessage.value);
    showPopup.value = false;
  }
}

// 从弹窗编辑消息
function handleEditFromPopup() {
  if (currentLongPressMessage.value) {
    currentEditContent.value = getMessageContent(currentLongPressMessage.value);
    showPopup.value = false;
    showEditModal.value = true;
  }
}

// 格式化文件大小
const formatFileSize = size => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};

// 从弹窗删除消息
function handleDeleteFromPopup() {
  if (currentLongPressMessage.value) {
    emit('deleteMessage', currentLongPressMessage.value);
    showPopup.value = false;
  }
}

// 处理编辑确认
function handleEditConfirm(content) {
  if (currentLongPressMessage.value) {
    const originalContent = getMessageContent(currentLongPressMessage.value);
    // 只有内容变更时才发送
    if (content !== originalContent) {
      emit('retryMessage', {
        ...currentLongPressMessage.value,
        content,
      });
    }
  }
}

// 获取消息内容
function getMessageContent(item) {
  if (!item || !item.value?.length) return '';

  // 处理不同类型的消息
  let content = '';

  for (const valueItem of item.value) {
    if (valueItem.type === 'text' && valueItem.text?.content) {
      content += valueItem.text.content;
    }
  }

  return content;
}

// 获取思考内容
function getReasoningContent(item) {
  if (!item || !item.value?.length) return '';

  // 查找 value 数组中 type 为 'reasoning' 的元素
  const reasoningItem = item.value.find(valueItem => valueItem.type === 'reasoning');

  if (reasoningItem && reasoningItem.reasoning?.content) {
    return reasoningItem.reasoning.content;
  }

  return '';
}

// 判断是否有文件
function hasFiles(item) {
  console.log('hasFiles', item);
  if (!item || !item.value?.length) return false;

  return item.value.some(valueItem => valueItem.type === 'file' || valueItem.file);
}

// 获取文件列表
function getFiles(item) {
  if (!item || !item.value?.length) return [];

  return item.value
    .filter(valueItem => valueItem.type === 'file' && valueItem.file)
    .map(valueItem => valueItem.file);
}

// 获取显示的文件列表（考虑展开/收起状态）
function getDisplayFiles(item) {
  console.log('getDisplayFiles', item);
  const files = getFiles(item);
  const messageId = item.dataId || '';
  // 如果文件数量小于等于3或者该消息的文件列表已展开，则返回所有文件
  if (files.length <= 3 || expandedFileListsMap[messageId]) {
    return files;
  }

  // 否则只返回前3个文件
  return files.slice(0, 3);
}

// 切换文件列表的展开/收起状态
function toggleFileListExpand(messageId) {
  if (expandedFileListsMap[messageId]) {
    expandedFileListsMap[messageId] = false;
  } else {
    expandedFileListsMap[messageId] = true;
  }
}

// 打开文件
function openFile(file) {
  if (file.type === 'image') {
    uni.previewImage({
      urls: [file.url],
      current: 0,
    });
  } else {
    // 处理其他类型文件
    uni.showLoading({ title: '打开文件中...' });

    uni.downloadFile({
      url: file.url,
      success: res => {
        uni.hideLoading();
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: function () {
              console.log('打开文档成功');
            },
            fail: function (err) {
              console.error('打开文档失败', err);
              uni.showToast({
                title: '打开文件失败',
                icon: 'none',
              });
            },
          });
        }
      },
      fail: () => {
        uni.hideLoading();
        uni.showToast({
          title: '下载文件失败',
          icon: 'none',
        });
      },
    });
  }
}
</script>

<style scoped>
.message-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

.welcome-message {
  display: flex;
  margin-bottom: 24rpx;
  transition: all 0.3s ease-in-out;
}

.centered-welcome {
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 200rpx;
}

.top-welcome {
  justify-content: flex-start;
  margin-top: 0;
}

.welcome-content {
  background-color: inherit;
  display: inline-block;
  max-width: 100%;
  word-wrap: break-word;
}

.app-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
}

.user-message-item {
  flex-direction: row-reverse;
}

.ai-message-item {
  flex-direction: row;
}

.user-avatar,
.ai-avatar {
  width: 40rpx;
  height: 40rpx;
  flex-shrink: 0;
  margin: 0 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #3d7fff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
}

.avatar-default {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 用户消息整体包装器 */
.user-message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 100%;
}

/* 用户消息内容容器 */
.user-message-content {
  max-width: 70%;
  margin-bottom: 8rpx;
}

/* 用户文件列表包装器 */
.user-file-list-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

/* 用户文件列表容器 */
.user-file-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  max-width: 80%;
  position: relative;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 24rpx;
  border-radius: 40rpx 40rpx 6rpx 40rpx;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  word-break: break-word;
  gap: 10rpx;
}

.user-message {
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  flex-wrap: wrap;
  background-color: #7d4dff;
  color: white;
}

.ai-message {
  background-color: #f5f5f5;
  color: #1d2129;
  border-radius: 40rpx 40rpx 40rpx 6rpx;
}

.loading-indicator {
  padding: 12rpx 16rpx;
  display: flex;
  align-items: center;
}

.loading-spinner {
  display: inline-block;
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #d0d0d0;
  border-top-color: #3d7fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.feedback-options {
  position: absolute;
  top: 8rpx;
  right: -32rpx;
}

.feedback-button {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.more-icon::before {
  content: '⋮';
  font-size: 24rpx;
  color: #666;
}

.options-menu {
  position: absolute;
  top: 28rpx;
  right: 0;
  background-color: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  min-width: 120rpx;
  z-index: 10;
}

.option-item {
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.option-item:hover {
  background-color: #f5f5f5;
}

.option-item.selected {
  color: #3d7fff;
}

.option-item text {
  margin-right: 8rpx;
}

.like-icon::before {
  content: '👍';
  font-size: 16rpx;
}

.dislike-icon::before {
  content: '👎';
  font-size: 16rpx;
}

.retry-icon::before {
  content: '↻';
  font-size: 16rpx;
}

.copy-icon::before {
  content: '📋';
  font-size: 16rpx;
}

.file-list {
  flex-wrap: wrap;
  margin-top: 8rpx;
  margin-bottom: 8rpx;
  gap: 8rpx;
  position: relative;
}

.expand-toggle {
  position: absolute;
  bottom: -48rpx;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  cursor: pointer;
  font-size: 24rpx;
  color: #7d4dff;
}

.expand-icon {
  margin-left: 4rpx;
}

.file-item {
  cursor: pointer;
  border-radius: 8rpx;
  overflow: hidden;
}

.file-image {
  width: 184rpx;
  height: 172rpx;
  border-radius: 16rpx;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 0px 9.5px 0px rgba(62, 71, 83, 0.12);
  overflow: hidden;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-document {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  background-color: #f7f7f7;
}

.file-icon {
  margin-right: 8rpx;
}

.document-icon::before {
  content: '📄';
  font-size: 24rpx;
}

.file-name {
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 消息操作栏样式 */
.message-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 4rpx 0;
}

.action-list {
  display: flex;
  margin-right: 68rpx;
}
.action-list:last-child {
  margin-right: 0rpx;
}

.action-item {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-item:last-child {
  margin-right: 0;
}

/* 长按弹出层样式 */
.longpress-popup {
  position: fixed;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  z-index: 1001;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
}

.popup-item {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  cursor: pointer;
}

.popup-item text {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #333;
}

/* 遮罩层样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000;
}
.dynamic-link {
  color: #2450b5;
  cursor: pointer;
  text-decoration: underline;
}
.upload-file-list-item {
  width: 100%;
  margin-bottom: 20rpx;
  justify-content: flex-start;
  align-items: center;
}

.file-card {
  display: flex;
  width: 494rpx;
  height: 122rpx;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  box-sizing: border-box;
  border: 1px solid #E7E7E7;
  padding: 12rpx 158rpx 12rpx 12rpx;
}

.file-icon-placeholder {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  margin-right: 24rpx;
}

/* 用户文件项样式 */
.user-file-item {
  cursor: pointer;
  border-radius: 8rpx;
  overflow: hidden;
}

/* 用户文件图片样式 */
.user-file-image {
  width: 184rpx;
  height: 172rpx;
  border-radius: 16rpx;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 0px 9.5px 0px rgba(62, 71, 83, 0.12);
  overflow: hidden;
}

/* 用户文件文档样式 */
.user-file-document {
  width: 100%;
  margin-bottom: 8rpx;
  justify-content: flex-start;
  align-items: center;
}

/* 用户文件卡片样式 */
.user-file-card {
  display: flex;
  width: 100%;
  max-width: 400rpx;
  height: 122rpx;
  flex-direction: row;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 28rpx;
  box-sizing: border-box;
  border: 1px solid #E7E7E7;
}

/* 用户展开/收起按钮 */
.user-expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
  cursor: pointer;
  font-size: 24rpx;
  color: #7d4dff;
  align-self: flex-end;
}

.file-details-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.file-name {
  font-size: 24rpx;
  font-weight: 400;
  color: #1d2129;
  line-height: 1.2;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-info {
  font-size: 24rpx;
  color: #909399;
  font-weight: 400;
  line-height: 1.2;
}
</style>
