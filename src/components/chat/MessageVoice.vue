<template>
  <view class="voice-container">
    <view
      class="voice-button"
      :class="{ recording: isRecording, cancel: isCancel }"
      @touchstart="startRecording"
      @touchmove="moveRecording"
      @touchend="endRecording"
    >
      <view v-show="isCancel"> 松手取消 </view>
      <view v-show="!isCancel && isRecording" class="recording-dots">
        <span v-for="n in 30" :key="n" class="dot"></span>
      </view>
      <view v-show="!isCancel && !isRecording" class="voice-button-content">
        <view @touchstart="prepareSwitchToUpload" @touchend="executeSwitchToUpload">
          <LkSvg width="64rpx" height="64rpx" src="/static/chat/addfile.svg" />
        </view>
        <view> 按住说话 </view>
        <view @touchstart="prepareSwitchToKeyboard" @touchend="executeSwitchToKeyboard">
          <LkSvg width="64rpx" height="64rpx" src="/static/chat/keyboard.svg" />
        </view>
      </view>
    </view>

    <!-- Custom Permission Modal -->
    <PermissionModal
      v-model:show="isShowVoicePermissionModal"
      title="语音权限未开启"
      content="检测到手机设置中未对APP 开启语音授权，请先在手机设置开启。"
      cancel-text="取消"
      confirm-text="前往设置"
      @cancel="handleModalCancel"
      @confirm="handleModalConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import LkSvg from '../svg/index.vue';
import PermissionModal from '../LKpermissionModal/index.vue'; // 导入自定义模态框

// 定义组件名称
defineOptions({
  name: 'MessageVoice',
});

// 定义事件
const emit = defineEmits([
  'start-recording',
  'cancel-recording',
  'finish-recording',
  'switch-to-upload',
  'switch-to-keyboard',
]);

// 响应式状态
const isRecording = ref(false);
const isCancel = ref(false);
const startY = ref(0);
const isSwitchingView = ref(false); // 标志是否正在切换视图
const isShowVoicePermissionModal = ref(false); // 控制自定义模态框的显示

// 检查并请求语音权限
const checkAndRequestVoicePermission = async (): Promise<boolean> => {
  return new Promise(resolve => {
    // #ifdef APP-PLUS
    // 检查当前权限状态
    const appAuthorizeSetting = uni.getAppAuthorizeSetting();
    const microphoneAuthorized = appAuthorizeSetting.microphoneAuthorized;

    if (microphoneAuthorized === 'authorized') {
      resolve(true);
      return;
    }

    if (microphoneAuthorized === 'denied') {
      // 用户已拒绝授权，提示用户去设置中开启
      showPermissionModal();
      resolve(false);
      return;
    }

    // 'not determined' 或其他情况，发起授权请求
    // Android平台需要先通过 requestPermissions 请求权限，iOS平台在调用相关API时会自动弹出授权框
    // 为了统一处理和更好的用户体验，可以先尝试请求。
    // 对于录音功能，通常是RecorderManager在start时处理权限。
    // 这里我们先用 uni.authorize 尝试，如果不行，实际录音时 RecorderManager 也会触发。
    uni.authorize({
      scope: 'scope.record',
      success: () => {
        resolve(true);
      },
      fail: () => {
        // 用户在授权弹窗中拒绝
        showPermissionModal();
        resolve(false);
      },
    });
    // #endif

    // #ifndef APP-PLUS
    // 非App端，直接认为有权限或不处理，具体逻辑视平台需求而定
    console.warn('语音权限检查仅在App端生效');
    resolve(true); // 默认返回 true，以便在非App平台也能继续流程
    // #endif
  });
};

// 修改为显示自定义模态框
const showPermissionModal = () => {
  isShowVoicePermissionModal.value = true;
};

// 自定义模态框的取消操作
const handleModalCancel = () => {
  isShowVoicePermissionModal.value = false;
  console.log('用户点击自定义模态框取消授权');
};

// 自定义模态框的确认操作 (前往设置)
const handleModalConfirm = () => {
  isShowVoicePermissionModal.value = false;
  // #ifdef APP-PLUS
  uni.openAppAuthorizeSetting({
    success: resOpen => {
      console.log('打开设置页面成功', resOpen);
    },
    fail: err => {
      console.error('打开设置页面失败', err);
      uni.showToast({
        title: '无法自动跳转，请手动前往系统设置开启权限',
        icon: 'none',
        duration: 3000,
      });
    },
  });
  // #endif
  // #ifndef APP-PLUS
  console.warn('打开应用设置功能仅在App端支持');
  // #endif
};

const prepareSwitchBase = () => {
  isSwitchingView.value = true;
};

const prepareSwitchToUpload = () => {
  prepareSwitchBase();
};

const prepareSwitchToKeyboard = () => {
  prepareSwitchBase();
};

const executeSwitchToUpload = () => {
  if (isSwitchingView.value) {
    emit('switch-to-upload');
    isSwitchingView.value = false;
    if (isRecording.value) {
      // 如果意外启动了录音，取消它
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
  }
};

const executeSwitchToKeyboard = () => {
  if (isSwitchingView.value) {
    emit('switch-to-keyboard');
    isSwitchingView.value = false;
    if (isRecording.value) {
      // 如果意外启动了录音，取消它
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
  }
};

// 开始录音
const startRecording = async (event: TouchEvent) => {
  if (isSwitchingView.value) {
    return;
  }

  // #ifdef APP-PLUS
  const hasPermission = await checkAndRequestVoicePermission();
  if (!hasPermission) {
    return;
  }
  // #endif

  setTimeout(() => {
    if (isSwitchingView.value) {
      return;
    }
    isRecording.value = true;
    isCancel.value = false;
    if (event.touches && event.touches.length > 0) {
      startY.value = event.touches[0].clientY;
    }
    emit('start-recording');
  }, 50);
};

// 移动中
const moveRecording = (event: TouchEvent) => {
  if (isSwitchingView.value) return;
  if (!isRecording.value) return;

  event.preventDefault();

  if (event.touches && event.touches.length > 0) {
    const currentY = event.touches[0].clientY;
    const moveDistance = startY.value - currentY;

    // 向上滑动超过50px时，进入取消状态
    if (moveDistance > 50) {
      isCancel.value = true;
    } else {
      isCancel.value = false;
    }
  }
};

// 结束录音
const endRecording = () => {
  console.log('endRecording');
  if (isSwitchingView.value) {
    isSwitchingView.value = false; // 清除标志
    if (isRecording.value) {
      // 如果录音被意外启动
      isRecording.value = false;
      isCancel.value = false;
      emit('cancel-recording');
    }
    return;
  }

  if (isRecording.value) {
    if (isCancel.value) {
      emit('cancel-recording');
    } else {
      emit('finish-recording');
    }
    isRecording.value = false;
    isCancel.value = false;
  }
};
</script>

<style scoped>
.voice-container {
  flex-shrink: 0;
  margin-left: auto;
  margin-right: auto;
}

.voice-button {
  width: 710rpx;
  height: 112rpx;
  border-radius: 100rpx;
  border: 1px solid var(--Gray-Gray1, #f3f3f3);
  background: #fff;
  box-shadow:
    0px 2px 4px 0px rgba(0, 0, 0, 0.05),
    0px 0px 16.7px -4px rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #1d2129;
  padding: 0 27rpx;
  font-weight: 600;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.voice-button.recording {
  background-color: #1976d2;
  color: white;
  border: none;
}

.voice-button.cancel {
  background-color: #f44336;
  color: white;
  border: none;
}

.recording-dots {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
  margin: 0 2px;
}

/* 可以添加一个动画效果使点看起来有波动感 */
@keyframes pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

.dot {
  animation: pulse 1.5s infinite;
  animation-delay: calc(var(--n) * 0.05s);
}
.voice-button-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
