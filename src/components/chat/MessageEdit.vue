<template>
  <view class="message-edit-container" v-if="visible">
    <view class="message-edit-box">
      <textarea
        class="message-edit-input"
        v-model="editContent"
        auto-height
        :maxlength="-1"
        :focus="visible"
        :adjust-position="false"
        @keyboardheightchange="handleKeyboardHeightChange"
      />
      <view class="message-edit-controls">
        <view class="control-button cancel" @tap="handleCancel">
          <view class="circle-button">
            <text class="icon-close">×</text>
          </view>
        </view>
        <view class="control-button confirm" @tap="handleConfirm">
          <view class="circle-button confirm-button">
            <view class="icon-check">✓</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['cancel', 'confirm', 'update:visible']);

const editContent = ref(props.content);
const keyboardHeight = ref(0);

// 监听props.content的变化
watch(
  () => props.content,
  newVal => {
    editContent.value = newVal;
  }
);

// 监听props.visible的变化
watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      editContent.value = props.content;
    }
  }
);

// 处理键盘高度变化
const handleKeyboardHeightChange = (e: any) => {
  keyboardHeight.value = e.detail.height;
};

// 处理取消编辑
const handleCancel = () => {
  emits('cancel');
  emits('update:visible', false);
};

// 处理确认编辑
const handleConfirm = () => {
  emits('confirm', editContent.value);
  emits('update:visible', false);
};
</script>

<style scoped>
.message-edit-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-edit-box {
  width: 690rpx;
  margin: 30rpx;
  background-color: #fff;
  border-radius: 36rpx;
  overflow: hidden;
  position: relative;
  border: 2rpx solid #e7eaef;
}

.message-edit-input {
  width: 100%;
  min-height: 120rpx;
  padding: 30rpx 100rpx 30rpx 30rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  box-sizing: border-box;
  border: none;
}

.message-edit-controls {
  display: flex;
  justify-content: space-between;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  pointer-events: none;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  width: 80rpx;
  height: 100%;
}

.cancel {
  padding-left: 20rpx;
}

.confirm {
  padding-right: 20rpx;
}

.circle-button {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9e9eb;
}

.icon-close {
  font-size: 36rpx;
  line-height: 1;
  color: #333;
  text-align: center;
}

.confirm-button {
  background-color: #7c4dff;
}

.icon-check {
  font-size: 28rpx;
  color: white;
}
</style>
