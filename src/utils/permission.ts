import { createApp, reactive, h, type App as VueApp } from 'vue';
import PermissionPopupComponent from '../components/PermissionPopup/index.vue';

interface PermissionDetails {
  scope: keyof UniApp.AuthSetting; // UniApp.AuthSetting的键，如 'scope.camera'
  deniedTitle: string;
  deniedMessage: string;
}

// 权限配置表
const permissionConfig: Record<string, PermissionDetails> = {
  camera: {
    scope: 'scope.camera',
    deniedTitle: '相机权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启相机权限，请先在手机设置开启。',
  },
  microphone: {
    // 用于通用麦克风权限
    scope: 'scope.record',
    deniedTitle: '麦克风权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启麦克风权限，请先在手机设置开启。',
  },
  voice: {
    // 特指图片中 "语音权限" (实际检查麦克风，但提示文字不同)
    scope: 'scope.record', // 实际检查的权限
    deniedTitle: '语音权限未开启', // 图片中的标题
    deniedMessage: '检测到手机设置中未对APP开启语音播放权限，请先在手机设置开启。', // 图片中的消息
  },
  album: {
    scope: 'scope.writePhotosAlbum', // UniApp标准scope，用于写入相册
    deniedTitle: '相册权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启相册写入权限，请先在手机设置开启以便保存图片。',
  },
  location: {
    scope: 'scope.userLocation',
    deniedTitle: '位置权限未开启',
    deniedMessage: '检测到手机设置中未对APP开启位置权限，请获取您的位置信息以便提供更精准的服务。',
  },
  // 您可以在此添加更多权限配置...
};

interface PopupState {
  visible: boolean;
  title: string;
  message: string;
  onGoToSettingsCallback: () => void;
  onCloseCallback: () => void;
}

const popupState = reactive<PopupState>({
  visible: false,
  title: '',
  message: '',
  onGoToSettingsCallback: () => {},
  onCloseCallback: () => {},
});

let vueAppInstance: VueApp | null = null;
let popupContainerElement: HTMLElement | null = null;

// #ifdef APP-PLUS

function judgeIosPermissionLocationNative(): boolean {
  let result = false;
  try {
    const cllocationManger = (plus.ios as any).import('CLLocationManager') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorizedAlways, 4: authorizedWhenInUse
    const status = cllocationManger.authorizationStatus();
    result = status !== 2 && status !== 1 && status !== 0; // true if authorized (3 or 4)
    console.log('[Permission iOS] Location authorizationStatus:', status, 'Result:', result);
    plus.ios.deleteObject(cllocationManger);
  } catch (e) {
    console.error('[Permission iOS] Error checking location permission:', e);
  }
  return result;
}

function judgeIosPermissionRecordNative(): boolean {
  let result = false;
  try {
    const avaudiosession = (plus.ios as any).import('AVAudioSession') as any;
    const avaudio = avaudiosession.sharedInstance();
    // 1684369017: 'denied', 1735552628: 'granted', 1970168948: 'undetermined'
    const permissionStatus = avaudio.recordPermission();
    result = permissionStatus === 1735552628; // true if 'granted'
    console.log('[Permission iOS] Record permissionStatus:', permissionStatus, 'Result:', result);
    plus.ios.deleteObject(avaudiosession); // avaudio is a shared instance, usually not deleted. Check Plus docs.
  } catch (e) {
    console.error('[Permission iOS] Error checking record permission:', e);
  }
  return result;
}

function judgeIosPermissionCameraNative(): boolean {
  let result = false;
  try {
    const AVCaptureDevice = (plus.ios as any).import('AVCaptureDevice') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorized
    const authStatus = AVCaptureDevice.authorizationStatusForMediaType('vide');
    result = authStatus === 3; // true if 'authorized'
    console.log('[Permission iOS] Camera authStatus:', authStatus, 'Result:', result);
    plus.ios.deleteObject(AVCaptureDevice);
  } catch (e) {
    console.error('[Permission iOS] Error checking camera permission:', e);
  }
  return result;
}

function judgeIosPermissionPhotoLibraryNative(): boolean {
  let result = false;
  try {
    const PHPhotoLibrary = (plus.ios as any).import('PHPhotoLibrary') as any;
    // 0: notDetermined, 1: restricted, 2: denied, 3: authorized, 4: limited (iOS 14+)
    const authStatus = PHPhotoLibrary.authorizationStatus();
    result = authStatus === 3 || authStatus === 4; // true if 'authorized' or 'limited'
    console.log('[Permission iOS] PhotoLibrary authStatus:', authStatus, 'Result:', result);
    plus.ios.deleteObject(PHPhotoLibrary);
  } catch (e) {
    console.error('[Permission iOS] Error checking photo library permission:', e);
  }
  return result;
}

function dispatchJudgeIosPermissionNative(permissionId: string): boolean {
  switch (permissionId) {
    case 'location':
      return judgeIosPermissionLocationNative();
    case 'camera':
      return judgeIosPermissionCameraNative();
    case 'photoLibrary':
      return judgeIosPermissionPhotoLibraryNative();
    case 'record':
      return judgeIosPermissionRecordNative();
    default:
      console.warn(
        `[Permission iOS] Unknown permissionID for dispatchJudgeIosPermissionNative: ${permissionId}`
      );
      return false;
  }
}

function requestAndroidPermissionNative(permissionID: string): Promise<number> {
  return new Promise(resolve => {
    plus.android.requestPermissions(
      [permissionID],
      (resultObj: any) => {
        let resultStatus = 0; // Default to denied
        if (resultObj.granted.length > 0) {
          console.log('[Permission Android] Granted:', resultObj.granted.join(', '));
          resultStatus = 1; // Granted
        } else if (resultObj.deniedAlways.length > 0) {
          console.log('[Permission Android] Denied Always:', resultObj.deniedAlways.join(', '));
          resultStatus = -1; // Denied always
        } else if (resultObj.deniedPresent.length > 0) {
          console.log('[Permission Android] Denied Present:', resultObj.deniedPresent.join(', '));
          resultStatus = 0; // Denied
        }
        resolve(resultStatus);
      },
      (error: any) => {
        console.error('[Permission Android] Request permission error:', error.code, error.message);
        resolve(0); // Treat error as denied
      }
    );
  });
}

function gotoAppPermissionSettingNative() {
  const isIOS = plus.os.name === 'iOS';
  if (isIOS) {
    try {
      const UIApplication = (plus.ios as any).import('UIApplication') as any;
      const application = UIApplication.sharedApplication();
      const NSURL = (plus.ios as any).import('NSURL') as any;
      const settingURL = NSURL.URLWithString('app-settings:');
      application.openURL(settingURL);
      plus.ios.deleteObject(settingURL);
      plus.ios.deleteObject(NSURL);
      plus.ios.deleteObject(application); // UIApplication is shared, check if deletion is correct.
    } catch (e) {
      console.error('[Permission iOS] Error opening app settings:', e);
      uni.showToast({ title: '无法打开应用设置', icon: 'none' });
    }
  } else {
    // Android
    try {
      const Intent = plus.android.importClass('android.content.Intent') as any;
      const Settings = plus.android.importClass('android.provider.Settings') as any;
      const Uri = plus.android.importClass('android.net.Uri') as any;
      const mainActivity = plus.android.runtimeMainActivity() as any;
      const intent = new Intent();
      intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
      const uri = Uri.fromParts('package', mainActivity.getPackageName(), null);
      intent.setData(uri);
      mainActivity.startActivity(intent);
    } catch (e) {
      console.error('[Permission Android] Error opening app settings:', e);
      uni.showToast({ title: '无法打开应用设置', icon: 'none' });
    }
  }
}

function mapScopeToIOSPermissionId(scope: keyof UniApp.AuthSetting): string | null {
  switch (scope) {
    case 'scope.camera':
      return 'camera';
    case 'scope.record':
      return 'record';
    case 'scope.writePhotosAlbum':
      return 'photoLibrary';
    case 'scope.userLocation':
      return 'location';
    default:
      console.warn(`[Permission Map] No iOS permission ID mapping for scope: ${scope}`);
      return null;
  }
}

function mapScopeToAndroidPermissionString(scope: keyof UniApp.AuthSetting): string | null {
  switch (scope) {
    case 'scope.camera':
      return 'android.permission.CAMERA';
    case 'scope.record':
      return 'android.permission.RECORD_AUDIO';
    case 'scope.writePhotosAlbum':
      return 'android.permission.WRITE_EXTERNAL_STORAGE'; // Consider READ_MEDIA_IMAGES for Android 13+
    case 'scope.userLocation':
      return 'android.permission.ACCESS_FINE_LOCATION';
    default:
      console.warn(`[Permission Map] No Android permission string mapping for scope: ${scope}`);
      return null;
  }
}

// --- End: Native permission helpers ---
// #endif

function ensurePopupInstance() {
  if (!vueAppInstance) {
    if (typeof document !== 'undefined') {
      // 确保在可以操作 DOM 的环境
      if (!popupContainerElement) {
        popupContainerElement = document.createElement('div');
        popupContainerElement.id = 'global-permission-popup-container';
        document.body.appendChild(popupContainerElement);
      }

      const RootComponent = {
        setup() {
          return () =>
            // Render function
            h(PermissionPopupComponent, {
              visible: popupState.visible,
              title: popupState.title,
              message: popupState.message,
              onClose: () => {
                popupState.visible = false;
                popupState.onCloseCallback();
              },
              onGoToSettings: () => {
                popupState.visible = false;
                popupState.onGoToSettingsCallback();
              },
            });
        },
      };

      vueAppInstance = createApp(RootComponent);
      vueAppInstance.mount(popupContainerElement);
    } else {
      console.warn(
        '[Permission] Document is not available, cannot create popup instance for custom UI.'
      );
    }
  }
}

function showActualPopup(details: PermissionDetails, resolvePromise: (granted: boolean) => void) {
  const openSettingsHandler = () => {
    // #ifdef APP-PLUS
    gotoAppPermissionSettingNative();
    resolvePromise(false); // Assume user needs to re-check after changing settings
    // #endif
    // #ifndef APP-PLUS
    // For non-app environments like MiniPrograms or H5, uni.openSetting might be available for some specific settings.
    // However, generic app permission settings are usually not accessible this way.
    // The original code tries uni.openSetting for non-APP-PLUS.
    if (typeof uni.openSetting === 'function') {
      uni.openSetting({
        success: res => {
          console.log('[Permission] Opened settings via uni.openSetting:', res.authSetting);
          // Check the specific permission again or assume user might have changed it.
          // For simplicity, we resolve false and let the app re-check.
          resolvePromise(false);
        },
        fail: err => {
          console.error('[Permission] Failed to open settings via uni.openSetting:', err);
          uni.showToast({ title: '无法打开设置页面', icon: 'none' });
          resolvePromise(false);
        },
      });
    } else {
      uni.showToast({ title: '此环境不支持打开设置页面', icon: 'none' });
      resolvePromise(false);
    }
    // #endif
  };

  if (typeof document === 'undefined' || !vueAppInstance) {
    // Fallback to uni.showModal if custom Vue popup cannot be used or in non-DOM APP environments
    console.log('[Permission] Using uni.showModal as fallback for permission prompt.');
    uni.showModal({
      title: details.deniedTitle,
      content: details.deniedMessage,
      showCancel: true,
      confirmText: '前往设置',
      cancelText: '取消',
      success: res => {
        if (res.confirm) {
          openSettingsHandler();
        } else {
          resolvePromise(false);
        }
      },
      fail: () => {
        resolvePromise(false);
      },
    });
    return;
  }

  // Use custom Vue popup
  ensurePopupInstance(); // Ensure it's really there if document was initially available
  if (!vueAppInstance) {
    // Double check after ensurePopupInstance if it failed silently
    console.error(
      '[Permission] Vue Popup instance still not available. Falling back to uni.showModal.'
    );
    // This is a fallback if ensurePopupInstance failed when document was present.
    // The logic is similar to the block above.
    uni.showModal({
      /* ... identical to above uni.showModal ... */ title: details.deniedTitle,
      content: details.deniedMessage,
      showCancel: true,
      confirmText: '前往设置',
      cancelText: '取消',
      success: res => {
        if (res.confirm) {
          openSettingsHandler();
        } else {
          resolvePromise(false);
        }
      },
      fail: () => {
        resolvePromise(false);
      },
    });
    return;
  }

  popupState.title = details.deniedTitle;
  popupState.message = details.deniedMessage;
  popupState.onCloseCallback = () => {
    resolvePromise(false);
  };
  popupState.onGoToSettingsCallback = openSettingsHandler;
  popupState.visible = true;
}

export function checkPermission(type: keyof typeof permissionConfig): Promise<boolean> {
  const details = permissionConfig[type];
  if (!details) {
    console.error(`[Permission] Unknown permission type: ${type}`);
    uni.showToast({ title: `未知权限类型: ${type}`, icon: 'none' });
    return Promise.resolve(false);
  }

  return new Promise<boolean>(resolve => {
    // #ifdef APP-PLUS
    const isIOS = plus.os.name === 'iOS';

    if (isIOS) {
      const iosPermissionId = mapScopeToIOSPermissionId(details.scope);
      if (!iosPermissionId) {
        console.error(`[Permission iOS] No mapping for scope ${details.scope}`);
        showActualPopup(details, resolve); // Fallback to show popup, implies denial
        return;
      }

      if (dispatchJudgeIosPermissionNative(iosPermissionId)) {
        resolve(true);
      } else {
        // Permission not currently granted (could be denied or not determined).
        // Attempt to request it using uni.authorize for a standard UX.
        console.log(
          `[Permission iOS] Native check failed for ${iosPermissionId}. Attempting uni.authorize for ${details.scope}`
        );
        uni.authorize({
          scope: details.scope,
          success: () => {
            console.log(`[Permission iOS] uni.authorize for ${details.scope} successful.`);
            resolve(true);
          },
          fail: err => {
            console.log(`[Permission iOS] uni.authorize for ${details.scope} failed:`, err);
            showActualPopup(details, resolve);
          },
        });
      }
    } else {
      // Android
      const androidPermissionString = mapScopeToAndroidPermissionString(details.scope);
      if (!androidPermissionString) {
        console.error(`[Permission Android] No mapping for scope ${details.scope}`);
        showActualPopup(details, resolve); // Fallback to show popup, implies denial
        return;
      }

      console.log(`[Permission Android] Requesting permission: ${androidPermissionString}`);
      requestAndroidPermissionNative(androidPermissionString)
        .then(status => {
          // status: 1 (granted), 0 (denied), -1 (denied always)
          if (status === 1) {
            console.log(`[Permission Android] ${androidPermissionString} granted.`);
            resolve(true);
          } else {
            console.log(
              `[Permission Android] ${androidPermissionString} denied (status: ${status}).`
            );
            showActualPopup(details, resolve);
          }
        })
        .catch(err => {
          // Should be handled within requestAndroidPermissionNative now
          console.error(
            `[Permission Android] requestAndroidPermissionNative promise rejected for ${androidPermissionString}:`,
            err
          );
          showActualPopup(details, resolve);
        });
    }
    // #endif

    // #ifndef APP-PLUS
    // For non-App platforms (H5, MiniPrograms), use uni.getSetting & uni.authorize
    // This logic remains the same as it's for non-Plus environments.
    console.log(`[Permission non-APP] Checking permission for ${details.scope}`);
    if (typeof uni.getSetting === 'function') {
      uni.getSetting({
        success: res => {
          const authSetting = res.authSetting as UniApp.AuthSetting;
          if (authSetting[details.scope] === undefined) {
            // Not yet requested or unknown
            console.log(
              `[Permission non-APP] ${details.scope} is undefined, attempting uni.authorize.`
            );
            if (typeof uni.authorize === 'function') {
              uni.authorize({
                scope: details.scope,
                success: () => {
                  console.log(
                    `[Permission non-APP] uni.authorize for ${details.scope} successful.`
                  );
                  resolve(true);
                },
                fail: err => {
                  console.log(
                    `[Permission non-APP] uni.authorize for ${details.scope} failed:`,
                    err
                  );
                  showActualPopup(details, resolve);
                },
              });
            } else {
              console.error(
                `[Permission non-APP] uni.authorize is not a function. Cannot request permission for ${details.scope}.`
              );
              showActualPopup(details, resolve);
            }
          } else if (authSetting[details.scope] === false) {
            // Explicitly denied
            console.log(`[Permission non-APP] ${details.scope} is false (denied).`);
            showActualPopup(details, resolve);
          } else {
            // Explicitly granted (true)
            console.log(`[Permission non-APP] ${details.scope} is true (granted).`);
            resolve(true);
          }
        },
        fail: err => {
          console.error(`[Permission non-APP] uni.getSetting for ${details.scope} failed:`, err);
          showActualPopup(details, resolve); // Fallback on error
        },
      });
    } else {
      console.warn(
        `[Permission non-APP] uni.getSetting is not available. Assuming no permission by default for ${details.scope}.`
      );
      showActualPopup(details, resolve); // Fallback if uni.getSetting isn't there
    }
    // #endif
  });
}
