import { resolveUrl } from './url';

interface TtsOptions {
  input: string;
  chatItemId?: string;
  onStart?: () => void;
  onComplete?: () => void;
  onError?: (error: any) => void;
  appId: string;
}

interface TtsControl {
  state: string;
  stop: () => void;
}

let activeTts: TtsControl | null = null;

declare const wx: {
  getFileSystemManager: () => {
    mkdirSync: (dirPath: string, recursive: boolean) => void;
    writeFile: (options: any) => void;
    readdir: (options: any) => void;
    unlink: (options: { filePath: string }) => void;
  };
  env: {
    USER_DATA_PATH: string;
  };
};

export const startTts = (options: TtsOptions): TtsControl => {
  const control: TtsControl = {
    state: '',
    stop: () => {},
  };

  let httpTask: UniApp.RequestTask | null = null;
  let audioContext: UniApp.InnerAudioContext | null = null;

  const clean = () => {
    if (activeTts === control) {
      activeTts = null;
    }
    control.state = '';
    httpTask?.abort();
    httpTask = null;
    audioContext?.stop();
    audioContext?.destroy();
    audioContext = null;
  };

  const getAudioUrl = (buffer: ArrayBuffer): Promise<string> => {
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager();
      const dirPath = `${wx.env.USER_DATA_PATH}/chatTts`;
      const filename = `${Date.now()}.mp3`;
      const filePath = `${dirPath}/${filename}`;
      try {
        fs.mkdirSync(dirPath, true);
      } catch (e) {
        // Directory may already exist
      }
      fs.writeFile({
        filePath,
        data: buffer,
        encoding: 'binary',
        success: () => {
          resolve(filePath);
        },
        fail: (err: any) => {
          console.log('ai tts save file error', err);
          reject(err);
        },
      });
      fs.readdir({
        dirPath,
        success: (res: { files?: string[] }) => {
          res?.files?.forEach((it: string) => {
            if (it !== filename) {
              fs.unlink({
                filePath: `${dirPath}/${it}`,
              });
            }
          });
        },
      });
    });
    // #endif

    // #ifdef H5
    const blob = new Blob([buffer], {
      type: 'audio/mp3',
    });
    return Promise.resolve(URL.createObjectURL(blob));
    // #endif

    // #ifdef APP-PLUS
    return new Promise((resolve, reject) => {
      const base64Data = uni.arrayBufferToBase64(buffer);
      const pureBase64 = base64Data.replace(/^data:audio\/\w+;base64,/, '');
      const fileName = `${Date.now()}.mp3`;
      plus.io.requestFileSystem(
        plus.io.PRIVATE_DOC,
        function (fs) {
          if (!fs.root) {
            reject(new Error('文件系统根目录不存在'));
            return;
          }
          fs.root.getFile(
            fileName,
            { create: true },
            function (entry) {
              const fullPath = entry.fullPath;
              const platform = uni.getSystemInfoSync().platform;
              if (platform === 'android') {
                try {
                  const Base64 = plus.android.importClass('android.util.Base64');
                  plus.android.importClass('java.io.FileOutputStream');
                  const out = plus.android.newObject('java.io.FileOutputStream', fullPath);
                  const bytes = plus.android.invoke(Base64, 'decode', [pureBase64, 0]);
                  plus.android.invoke(out, 'write', bytes);
                  plus.android.invoke(out, 'close');
                  resolve(entry.toLocalURL());
                } catch (e) {
                  reject(e);
                }
              } else if (platform === 'ios') {
                const NSData = plus.ios.importClass('NSData');
                let nsData = new NSData();
                nsData = nsData.initWithBase64EncodedStringoptions(pureBase64, 0);
                if (nsData) {
                  nsData.plusCallMethod({ writeToFile: fullPath, atomically: true });
                  plus.ios.deleteObject(nsData);
                }
                resolve(entry.toLocalURL());
              } else {
                reject(new Error('不支持的平台'));
              }
            },
            reject
          );
        },
        reject
      );
    });
    // #endif

    // Default case - return empty promise if platform not supported
    // #ifndef MP-WEIXIN || H5 || APP-PLUS
    return Promise.reject(new Error('Platform not supported'));
    // #endif
  };

  const playAudio = async (buffer: ArrayBuffer) => {
    let url = null;
    try {
      url = await getAudioUrl(buffer);
    } catch (err) {
      clean();
      return;
    }

    if (control.state !== 'starting') {
      return;
    }

    audioContext = uni.createInnerAudioContext();
    audioContext.src = url;
    audioContext.play();

    audioContext.onPlay(() => {
      if (!control.state) {
        return;
      }
      console.log('ai tts playAudio onPlay');
      control.state = 'playing';
    });

    audioContext.onStop(() => {
      if (!control.state) {
        return;
      }
      console.log('ai tts playAudio onStop');
      clean();
    });

    audioContext.onEnded(() => {
      if (!control.state) {
        return;
      }
      console.log('ai tts playAudio onEnded');
      clean();
    });

    audioContext.onError(err => {
      if (!control.state) {
        return;
      }
      console.log('ai tts playAudio onError', err);
      clean();
    });
  };

  const getAudioData = () => {
    if (control.state !== 'starting') {
      return;
    }

    console.log(options);

    httpTask = uni.request({
      url: resolveUrl('/huayun-ai/client/chat/item/speech'),
      method: 'POST',
      responseType: 'arraybuffer',
      header: {
        Authorization: uni.getStorageSync('token'),
      },
      data: {
        input: options.input,
        ttsConfig: {
          model: 'tts-1',
          speed: 1,
          type: 'model',
          voice: 'zhifeng_emo',
        },
        chatItemId: options.chatItemId,
        appId: options.appId,
      },
      success: res => {
        console.log('ai tts data success, res:', res);
        let buffer: ArrayBuffer | null = null;
        if (res.data instanceof ArrayBuffer && res.data.byteLength > 0) {
          buffer = res.data;
        } else if (
          typeof Uint8Array !== 'undefined' &&
          res.data instanceof Uint8Array &&
          res.data.byteLength > 0
        ) {
          buffer = res.data.buffer as ArrayBuffer;
        }
        if (!buffer) {
          console.log('ai tts data 返回空音频数据！', res);
          options.onError?.('音频数据为空');
          clean();
          return;
        }
        playAudio(buffer);
      },
      fail: err => {
        console.log('ai tts data fail', err);
        clean();
      },
    });
  };

  control.stop = () => {
    if (!control.state) {
      return;
    }
    console.log('ai tts stop');
    clean();
  };

  activeTts?.stop();
  activeTts = control;

  control.state = 'starting';
  getAudioData();

  return control;
};

export const stopTts = () => {
  activeTts?.stop();
};
