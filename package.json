{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:custom": "cross-env NODE_OPTIONS='--no-warnings' uni -p", "dev:h5": "cross-env NODE_OPTIONS='--no-warnings' uni", "dev:h5:ssr": "cross-env NODE_OPTIONS='--no-warnings' uni --ssr", "dev:h5:clean": "node run-without-warnings.js", "dev:h5:silent": "cross-env NODE_OPTIONS='--no-warnings' SASS_SILENCE_WARNINGS=1 SASS_SILENCE_DEPRECATION_WARNINGS=1 NO_DEPRECATION_WARNINGS=sass node ./node_modules/.bin/uni", "dev:h5:nowarning": "node sass-filter.js ./node_modules/.bin/uni", "dev:mp-alipay": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-alipay", "dev:mp-baidu": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-baidu", "dev:mp-jd": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-jd", "dev:mp-kuaishou": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-kuaishou", "dev:mp-lark": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-lark", "dev:mp-qq": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-qq", "dev:mp-toutiao": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-toutiao", "dev:mp-weixin": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-weixin", "dev:mp-xhs": "cross-env NODE_OPTIONS='--no-warnings' uni -p mp-xhs", "dev:quickapp-webview": "cross-env NODE_OPTIONS='--no-warnings' uni -p quickapp-webview", "dev:quickapp-webview-huawei": "cross-env NODE_OPTIONS='--no-warnings' uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "cross-env NODE_OPTIONS='--no-warnings' uni -p quickapp-webview-union", "dev:app": "cross-env NODE_OPTIONS='--no-warnings' uni -p app", "dev:app-android": "cross-env NODE_OPTIONS='--no-warnings' uni -p app-android", "dev:app-android:dev": "npm run dev:app-android", "dev:app-ios": "cross-env NODE_OPTIONS='--no-warnings' uni -p app-ios", "dev:app-ios:dev": "npm run dev:app-ios", "build:custom": "cross-env NODE_OPTIONS='--no-warnings' uni build -p", "build:h5": "cross-env NODE_OPTIONS='--no-warnings' uni build", "build:h5:ssr": "cross-env NODE_OPTIONS='--no-warnings' uni build --ssr", "build:mp-alipay": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-alipay", "build:mp-baidu": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-baidu", "build:mp-jd": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-jd", "build:mp-kuaishou": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-kuaishou", "build:mp-lark": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-lark", "build:mp-qq": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-qq", "build:mp-toutiao": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-toutiao", "build:mp-weixin": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-weixin", "build:mp-xhs": "cross-env NODE_OPTIONS='--no-warnings' uni build -p mp-xhs", "build:quickapp-webview": "cross-env NODE_OPTIONS='--no-warnings' uni build -p quickapp-webview", "build:quickapp-webview-huawei": "cross-env NODE_OPTIONS='--no-warnings' uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "cross-env NODE_OPTIONS='--no-warnings' uni build -p quickapp-webview-union", "build:app": "cross-env NODE_OPTIONS='--no-warnings' uni build -p app", "build:app-ios": "cross-env NODE_OPTIONS='--no-warnings' uni build -p app-ios", "type-check": "vue-tsc --noEmit", "format": "prettier --write \"src/**/*.{js,ts,vue,json,css,scss,md}\"", "prepare": "husky"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060420250429001", "@dcloudio/uni-app-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-app-plus": "3.0.0-4060420250429001", "@dcloudio/uni-components": "3.0.0-4060420250429001", "@dcloudio/uni-h5": "3.0.0-4060420250429001", "@dcloudio/uni-mp-alipay": "3.0.0-4060420250429001", "@dcloudio/uni-mp-baidu": "3.0.0-4060420250429001", "@dcloudio/uni-mp-harmony": "3.0.0-4060420250429001", "@dcloudio/uni-mp-jd": "3.0.0-4060420250429001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060420250429001", "@dcloudio/uni-mp-lark": "3.0.0-4060420250429001", "@dcloudio/uni-mp-qq": "3.0.0-4060420250429001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060420250429001", "@dcloudio/uni-mp-weixin": "3.0.0-4060420250429001", "@dcloudio/uni-mp-xhs": "3.0.0-4060420250429001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060420250429001", "@dcloudio/uni-ui": "^1.5.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "js-base64": "^3.7.7", "lottie-miniprogram": "^1.0.12", "lottie-web": "^5.12.2", "pinia": "2.1.7", "uview-plus": "3.4.28", "vue": "^3.4.21", "vue-click-to-component": "^0.3.7", "vue-i18n": "^9.1.9"}, "devDependencies": {"@antfu/eslint-config": "^4.13.0", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060420250429001", "@dcloudio/uni-cli-shared": "3.0.0-4060420250429001", "@dcloudio/uni-stacktracey": "3.0.0-4060420250429001", "@dcloudio/vite-plugin-uni": "3.0.0-4060420250429001", "@types/crypto-js": "^4.2.2", "@uni-helper/eslint-config": "^0.4.0", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "cross-env": "^7.0.3", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "sass": "1.63.2", "typescript": "^4.9.4", "vite": "5.2.8", "vue-tsc": "^1.0.24"}, "lint-staged": {"src/**/*.{js,ts,vue,json,css,scss,less,md}": "prettier --write"}}